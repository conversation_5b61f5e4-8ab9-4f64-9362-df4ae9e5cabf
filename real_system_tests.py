#!/usr/bin/env python3
"""
REAL SYSTEM TESTS - 20+ ACTUAL TESTS
No fake stuff, no simulations, only REAL AI model responses and performance
"""

import asyncio
import time
import json
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass

# Import systems for testing
from ultimate_ensemble_system import UltimateEnsembleSystem
from local_ensemble_voting_system import LocalEnsembleVotingSystem

@dataclass
class TestResult:
    """Real test result"""
    test_name: str
    passed: bool
    execution_time: float
    real_ai_calls: int
    actual_responses: int
    details: Dict[str, Any]
    error: str = ""

class RealSystemTester:
    """Real system testing - no fake stuff"""
    
    def __init__(self):
        self.ultimate_system = UltimateEnsembleSystem()
        self.local_system = LocalEnsembleVotingSystem()
        self.test_results = []
        self.total_ai_calls = 0
        self.total_responses = 0
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all real tests"""
        print("🧪 RUNNING 20+ REAL SYSTEM TESTS")
        print("=" * 60)
        print("🔥 ONLY REAL AI RESPONSES - NO FAKE DATA")
        print()
        
        tests = [
            self.test_system_initialization,
            self.test_emergency_mode,
            self.test_fast_mode,
            self.test_normal_mode,
            self.test_individual_models,
            self.test_ensemble_voting,
            self.test_cache_performance,
            self.test_response_times,
            self.test_confidence_scoring,
            self.test_consensus_calculation,
            self.test_risk_assessment,
            self.test_voting_strategies,
            self.test_meta_voting,
            self.test_parallel_execution,
            self.test_error_handling,
            self.test_timeout_handling,
            self.test_memory_usage,
            self.test_decision_consistency,
            self.test_high_load,
            self.test_rapid_requests,
            self.test_system_recovery,
            self.test_cross_system_compatibility
        ]
        
        for i, test in enumerate(tests, 1):
            print(f"🧪 Test {i:2d}: {test.__name__.replace('test_', '').replace('_', ' ').title()}")
            
            try:
                result = await test()
                self.test_results.append(result)
                
                status = "✅ PASSED" if result.passed else "❌ FAILED"
                print(f"   {status} - {result.execution_time:.2f}s - {result.real_ai_calls} AI calls")
                
                if not result.passed and result.error:
                    print(f"   Error: {result.error}")
                
            except Exception as e:
                failed_result = TestResult(
                    test_name=test.__name__,
                    passed=False,
                    execution_time=0.0,
                    real_ai_calls=0,
                    actual_responses=0,
                    details={},
                    error=str(e)
                )
                self.test_results.append(failed_result)
                print(f"   ❌ FAILED - Exception: {e}")
        
        return self.generate_report()
    
    async def test_system_initialization(self) -> TestResult:
        """Test 1: System initialization"""
        start_time = time.time()
        
        try:
            # Check systems are initialized
            ultimate_models = len(self.ultimate_system.models)
            local_models = len(self.local_system.models)
            
            passed = ultimate_models > 0 and local_models > 0
            
            return TestResult(
                test_name="System Initialization",
                passed=passed,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={
                    'ultimate_models': ultimate_models,
                    'local_models': local_models
                }
            )
        except Exception as e:
            return TestResult(
                test_name="System Initialization",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_emergency_mode(self) -> TestResult:
        """Test 2: Emergency mode performance"""
        start_time = time.time()
        
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "AAPL", 
                {"trend": "bullish", "volatility": "medium"}, 
                "emergency"
            )
            
            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)
            
            # Emergency mode should be fast and work
            passed = response_time < 30 and models_used > 0 and hasattr(decision, 'final_action')
            
            self.total_ai_calls += models_used
            self.total_responses += models_used
            
            return TestResult(
                test_name="Emergency Mode",
                passed=passed,
                execution_time=response_time,
                real_ai_calls=models_used,
                actual_responses=models_used,
                details={
                    'action': getattr(decision, 'final_action', 'UNKNOWN'),
                    'confidence': getattr(decision, 'ensemble_confidence', 0),
                    'models_used': models_used,
                    'response_time': response_time
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Emergency Mode",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_fast_mode(self) -> TestResult:
        """Test 3: Fast mode performance"""
        start_time = time.time()
        
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "TSLA", 
                {"trend": "bearish", "volatility": "high"}, 
                "fast"
            )
            
            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)
            
            passed = response_time < 45 and models_used > 0
            
            self.total_ai_calls += models_used
            self.total_responses += models_used
            
            return TestResult(
                test_name="Fast Mode",
                passed=passed,
                execution_time=response_time,
                real_ai_calls=models_used,
                actual_responses=models_used,
                details={
                    'action': getattr(decision, 'final_action', 'UNKNOWN'),
                    'confidence': getattr(decision, 'ensemble_confidence', 0),
                    'models_used': models_used
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Fast Mode",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_normal_mode(self) -> TestResult:
        """Test 4: Normal mode performance"""
        start_time = time.time()
        
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "BTC", 
                {"trend": "volatile", "volatility": "extreme"}, 
                "normal"
            )
            
            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)
            
            passed = response_time < 60 and models_used > 1
            
            self.total_ai_calls += models_used
            self.total_responses += models_used
            
            return TestResult(
                test_name="Normal Mode",
                passed=passed,
                execution_time=response_time,
                real_ai_calls=models_used,
                actual_responses=models_used,
                details={
                    'action': getattr(decision, 'final_action', 'UNKNOWN'),
                    'confidence': getattr(decision, 'ensemble_confidence', 0),
                    'models_used': models_used
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Normal Mode",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_individual_models(self) -> TestResult:
        """Test 5: Individual model calls"""
        start_time = time.time()
        
        try:
            # Test individual model calls
            successful_calls = 0
            total_calls = 0
            
            # Test lightning models
            lightning_models = [name for name, config in self.ultimate_system.models.items() 
                              if config.get('tier') == 'lightning'][:3]
            
            for model_name in lightning_models:
                total_calls += 1
                try:
                    result = self.ultimate_system._call_model_ultra_fast(
                        model_name,
                        "Quick test. ACTION: HOLD, CONFIDENCE: 0.5, REASONING: Test",
                        15,
                        self.ultimate_system.models[model_name]
                    )
                    
                    if result.success:
                        successful_calls += 1
                        self.total_ai_calls += 1
                        
                except Exception:
                    pass
            
            success_rate = successful_calls / total_calls if total_calls > 0 else 0
            passed = success_rate > 0.5  # 50% success rate minimum
            
            return TestResult(
                test_name="Individual Models",
                passed=passed,
                execution_time=time.time() - start_time,
                real_ai_calls=successful_calls,
                actual_responses=successful_calls,
                details={
                    'successful_calls': successful_calls,
                    'total_calls': total_calls,
                    'success_rate': success_rate,
                    'models_tested': lightning_models
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Individual Models",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_ensemble_voting(self) -> TestResult:
        """Test 6: Ensemble voting functionality"""
        start_time = time.time()
        
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "NVDA", 
                {"trend": "bullish", "volatility": "low"}, 
                "emergency"
            )
            
            models_used = getattr(decision, 'models_used', 0)
            has_action = hasattr(decision, 'final_action')
            has_confidence = hasattr(decision, 'ensemble_confidence')
            has_voting = hasattr(decision, 'voting_strategies')
            
            passed = models_used > 0 and has_action and has_confidence
            
            self.total_ai_calls += models_used
            self.total_responses += models_used
            
            return TestResult(
                test_name="Ensemble Voting",
                passed=passed,
                execution_time=time.time() - start_time,
                real_ai_calls=models_used,
                actual_responses=models_used,
                details={
                    'models_used': models_used,
                    'has_action': has_action,
                    'has_confidence': has_confidence,
                    'has_voting': has_voting,
                    'action': getattr(decision, 'final_action', 'UNKNOWN')
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Ensemble Voting",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    async def test_cache_performance(self) -> TestResult:
        """Test 7: Cache performance"""
        start_time = time.time()
        
        try:
            # First call
            first_start = time.time()
            decision1 = await self.ultimate_system.get_ultimate_trading_decision(
                "MSFT", {"trend": "neutral"}, "emergency"
            )
            first_time = time.time() - first_start
            
            # Second call (should hit cache)
            second_start = time.time()
            decision2 = await self.ultimate_system.get_ultimate_trading_decision(
                "MSFT", {"trend": "neutral"}, "emergency"
            )
            second_time = time.time() - second_start
            
            cache_hit = getattr(decision2, 'cache_hit', False)
            speedup = (first_time - second_time) / first_time if first_time > 0 else 0
            
            passed = cache_hit or speedup > 0.5
            models_used = getattr(decision1, 'models_used', 0)
            
            self.total_ai_calls += models_used
            self.total_responses += models_used
            
            return TestResult(
                test_name="Cache Performance",
                passed=passed,
                execution_time=time.time() - start_time,
                real_ai_calls=models_used,
                actual_responses=models_used,
                details={
                    'first_time': first_time,
                    'second_time': second_time,
                    'cache_hit': cache_hit,
                    'speedup': speedup
                }
            )
        except Exception as e:
            return TestResult(
                test_name="Cache Performance",
                passed=False,
                execution_time=time.time() - start_time,
                real_ai_calls=0,
                actual_responses=0,
                details={},
                error=str(e)
            )
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed_tests = sum(1 for r in self.test_results if r.passed)
        total_tests = len(self.test_results)
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        total_time = sum(r.execution_time for r in self.test_results)
        avg_time = total_time / total_tests if total_tests > 0 else 0
        
        return {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': success_rate,
                'total_execution_time': total_time,
                'avg_execution_time': avg_time,
                'total_ai_calls': self.total_ai_calls,
                'total_responses': self.total_responses
            },
            'test_results': [
                {
                    'test_name': r.test_name,
                    'passed': r.passed,
                    'execution_time': r.execution_time,
                    'real_ai_calls': r.real_ai_calls,
                    'details': r.details,
                    'error': r.error
                }
                for r in self.test_results
            ],
            'failed_tests': [
                {'test_name': r.test_name, 'error': r.error}
                for r in self.test_results if not r.passed
            ]
        }

    # Additional test methods (8-22)
    async def test_response_times(self) -> TestResult:
        """Test 8: Response time consistency"""
        start_time = time.time()
        try:
            times = []
            total_models = 0

            for symbol in ["AAPL", "TSLA", "BTC"]:
                test_start = time.time()
                decision = await self.ultimate_system.get_ultimate_trading_decision(symbol, {"trend": "neutral"}, "emergency")
                times.append(time.time() - test_start)
                total_models += getattr(decision, 'models_used', 0)

            avg_time = sum(times) / len(times)
            passed = avg_time < 25 and len(times) == 3

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("Response Times", passed, time.time() - start_time, total_models, total_models,
                            {'avg_time': avg_time, 'times': times})
        except Exception as e:
            return TestResult("Response Times", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_confidence_scoring(self) -> TestResult:
        """Test 9: Confidence scoring"""
        start_time = time.time()
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision("GOOGL", {"trend": "bullish"}, "normal")
            confidence = getattr(decision, 'ensemble_confidence', 0)
            models_used = getattr(decision, 'models_used', 0)

            passed = 0 <= confidence <= 1 and models_used > 0

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Confidence Scoring", passed, time.time() - start_time, models_used, models_used,
                            {'confidence': confidence, 'valid_range': 0 <= confidence <= 1})
        except Exception as e:
            return TestResult("Confidence Scoring", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_consensus_calculation(self) -> TestResult:
        """Test 10: Consensus calculation"""
        start_time = time.time()
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision("AMZN", {"trend": "volatile"}, "comprehensive")
            consensus = getattr(decision, 'consensus_level', 0)
            models_used = getattr(decision, 'models_used', 0)

            passed = 0 <= consensus <= 1 and models_used > 1

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Consensus Calculation", passed, time.time() - start_time, models_used, models_used,
                            {'consensus': consensus, 'models_used': models_used})
        except Exception as e:
            return TestResult("Consensus Calculation", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_risk_assessment(self) -> TestResult:
        """Test 11: Risk assessment"""
        start_time = time.time()
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision("META", {"trend": "bullish", "volatility": "high"}, "normal")
            risk_assessment = getattr(decision, 'risk_assessment', {})
            models_used = getattr(decision, 'models_used', 0)

            has_risk_level = 'risk_level' in risk_assessment
            has_overall_risk = 'overall_risk' in risk_assessment
            passed = has_risk_level and has_overall_risk and models_used > 0

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Risk Assessment", passed, time.time() - start_time, models_used, models_used,
                            {'has_risk_data': has_risk_level and has_overall_risk, 'risk_assessment': risk_assessment})
        except Exception as e:
            return TestResult("Risk Assessment", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_voting_strategies(self) -> TestResult:
        """Test 12: Voting strategies"""
        start_time = time.time()
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision("NFLX", {"trend": "bullish"}, "normal")
            voting_strategies = getattr(decision, 'voting_strategies', {})
            models_used = getattr(decision, 'models_used', 0)

            passed = len(voting_strategies) >= 2 and models_used > 0

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Voting Strategies", passed, time.time() - start_time, models_used, models_used,
                            {'strategies_count': len(voting_strategies), 'strategies': list(voting_strategies.keys())})
        except Exception as e:
            return TestResult("Voting Strategies", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_meta_voting(self) -> TestResult:
        """Test 13: Meta-voting"""
        start_time = time.time()
        try:
            decision = await self.ultimate_system.get_ultimate_trading_decision("UBER", {"trend": "neutral"}, "comprehensive")
            meta_voting = getattr(decision, 'meta_voting', False)
            models_used = getattr(decision, 'models_used', 0)

            passed = meta_voting and models_used > 1

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Meta Voting", passed, time.time() - start_time, models_used, models_used,
                            {'meta_voting_enabled': meta_voting, 'models_used': models_used})
        except Exception as e:
            return TestResult("Meta Voting", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_parallel_execution(self) -> TestResult:
        """Test 14: Parallel execution"""
        start_time = time.time()
        try:
            symbols = ["AAPL", "TSLA", "BTC"]
            tasks = [self.ultimate_system.get_ultimate_trading_decision(symbol, {"trend": "neutral"}, "emergency") for symbol in symbols]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful = [r for r in results if not isinstance(r, Exception)]

            total_models = sum(getattr(r, 'models_used', 0) for r in successful)
            passed = len(successful) >= 2

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("Parallel Execution", passed, time.time() - start_time, total_models, total_models,
                            {'successful_results': len(successful), 'total_symbols': len(symbols)})
        except Exception as e:
            return TestResult("Parallel Execution", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_error_handling(self) -> TestResult:
        """Test 15: Error handling"""
        start_time = time.time()
        try:
            # Test with invalid inputs
            try:
                await self.ultimate_system.get_ultimate_trading_decision("", {}, "invalid_urgency")
            except:
                pass  # Expected to fail

            # Test recovery
            decision = await self.ultimate_system.get_ultimate_trading_decision("AAPL", {"trend": "neutral"}, "emergency")
            models_used = getattr(decision, 'models_used', 0)

            passed = models_used > 0  # System recovered

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("Error Handling", passed, time.time() - start_time, models_used, models_used,
                            {'recovery_successful': models_used > 0})
        except Exception as e:
            return TestResult("Error Handling", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_timeout_handling(self) -> TestResult:
        """Test 16: Timeout handling"""
        start_time = time.time()
        try:
            # Test with very short timeout
            original_timeout = self.ultimate_system.timeout_base
            self.ultimate_system.timeout_base = 1  # Very short

            decision = await self.ultimate_system.get_ultimate_trading_decision("AAPL", {"trend": "neutral"}, "emergency")

            # Restore timeout
            self.ultimate_system.timeout_base = original_timeout

            has_action = hasattr(decision, 'final_action')
            passed = has_action

            return TestResult("Timeout Handling", passed, time.time() - start_time, 0, 0,
                            {'has_action': has_action, 'action': getattr(decision, 'final_action', 'NONE')})
        except Exception as e:
            return TestResult("Timeout Handling", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_memory_usage(self) -> TestResult:
        """Test 17: Memory usage"""
        start_time = time.time()
        try:
            import psutil
            process = psutil.Process()

            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Make several decisions
            for i in range(3):
                await self.ultimate_system.get_ultimate_trading_decision(f"TEST{i}", {"trend": "neutral"}, "emergency")

            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory

            passed = memory_increase < 50  # Less than 50MB increase

            return TestResult("Memory Usage", passed, time.time() - start_time, 0, 0,
                            {'initial_memory': initial_memory, 'final_memory': final_memory, 'increase': memory_increase})
        except Exception as e:
            return TestResult("Memory Usage", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_decision_consistency(self) -> TestResult:
        """Test 18: Decision consistency"""
        start_time = time.time()
        try:
            decisions = []
            total_models = 0

            for i in range(3):
                decision = await self.ultimate_system.get_ultimate_trading_decision("AAPL", {"trend": "bullish"}, "emergency")
                decisions.append(decision)
                total_models += getattr(decision, 'models_used', 0)

            actions = [getattr(d, 'final_action', 'UNKNOWN') for d in decisions]
            consistent = len(set(actions)) <= 2  # Allow some variation

            passed = consistent and len(decisions) == 3

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("Decision Consistency", passed, time.time() - start_time, total_models, total_models,
                            {'actions': actions, 'consistent': consistent})
        except Exception as e:
            return TestResult("Decision Consistency", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_high_load(self) -> TestResult:
        """Test 19: High load stress test"""
        start_time = time.time()
        try:
            symbols = ["AAPL", "TSLA", "BTC", "NVDA", "MSFT"] * 2  # 10 requests
            tasks = [self.ultimate_system.get_ultimate_trading_decision(symbol, {"trend": "neutral"}, "emergency") for symbol in symbols]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful = [r for r in results if not isinstance(r, Exception)]

            total_models = sum(getattr(r, 'models_used', 0) for r in successful)
            success_rate = len(successful) / len(tasks)
            passed = success_rate > 0.6  # 60% success under load

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("High Load", passed, time.time() - start_time, total_models, total_models,
                            {'total_requests': len(tasks), 'successful': len(successful), 'success_rate': success_rate})
        except Exception as e:
            return TestResult("High Load", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_rapid_requests(self) -> TestResult:
        """Test 20: Rapid sequential requests"""
        start_time = time.time()
        try:
            successful_requests = 0
            total_models = 0

            for i in range(5):
                try:
                    decision = await self.ultimate_system.get_ultimate_trading_decision(f"RAPID{i}", {"trend": "neutral"}, "emergency")
                    successful_requests += 1
                    total_models += getattr(decision, 'models_used', 0)
                except:
                    pass

            passed = successful_requests >= 3  # At least 3/5 should succeed

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("Rapid Requests", passed, time.time() - start_time, total_models, total_models,
                            {'successful_requests': successful_requests, 'total_requests': 5})
        except Exception as e:
            return TestResult("Rapid Requests", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_system_recovery(self) -> TestResult:
        """Test 21: System recovery"""
        start_time = time.time()
        try:
            # Cause an error
            try:
                await self.ultimate_system.get_ultimate_trading_decision("", {}, "invalid")
            except:
                pass  # Expected to fail

            # Test recovery
            decision = await self.ultimate_system.get_ultimate_trading_decision("AAPL", {"trend": "bullish"}, "emergency")
            models_used = getattr(decision, 'models_used', 0)

            passed = models_used > 0  # System recovered

            self.total_ai_calls += models_used
            self.total_responses += models_used

            return TestResult("System Recovery", passed, time.time() - start_time, models_used, models_used,
                            {'recovery_successful': models_used > 0})
        except Exception as e:
            return TestResult("System Recovery", False, time.time() - start_time, 0, 0, {}, str(e))

    async def test_cross_system_compatibility(self) -> TestResult:
        """Test 22: Cross-system compatibility"""
        start_time = time.time()
        try:
            successful_systems = 0
            total_models = 0

            # Test ultimate system
            try:
                decision1 = await self.ultimate_system.get_ultimate_trading_decision("AAPL", {"trend": "neutral"}, "emergency")
                if getattr(decision1, 'models_used', 0) > 0:
                    successful_systems += 1
                    total_models += getattr(decision1, 'models_used', 0)
            except:
                pass

            # Test local system
            try:
                decision2 = await self.local_system.get_ensemble_prediction("AAPL", {"trend": "neutral"})
                if getattr(decision2, 'models_used', 0) > 0:
                    successful_systems += 1
                    total_models += getattr(decision2, 'models_used', 0)
            except:
                pass

            passed = successful_systems >= 1  # At least 1 system should work

            self.total_ai_calls += total_models
            self.total_responses += total_models

            return TestResult("Cross-System Compatibility", passed, time.time() - start_time, total_models, total_models,
                            {'successful_systems': successful_systems, 'total_systems': 2})
        except Exception as e:
            return TestResult("Cross-System Compatibility", False, time.time() - start_time, 0, 0, {}, str(e))
async def main():
    """Run real system tests"""
    tester = RealSystemTester()
    report = await tester.run_all_tests()
    
    summary = report['summary']
    print(f"\n📊 REAL TEST RESULTS:")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']} ✅")
    print(f"   Failed: {summary['failed_tests']} ❌")
    print(f"   Success Rate: {summary['success_rate']:.1%}")
    print(f"   Total AI Calls: {summary['total_ai_calls']}")
    print(f"   Total Responses: {summary['total_responses']}")
    print(f"   Total Time: {summary['total_execution_time']:.1f}s")
    
    if report['failed_tests']:
        print(f"\n❌ FAILED TESTS:")
        for failed in report['failed_tests']:
            print(f"   - {failed['test_name']}: {failed['error']}")
    
    # Save report
    with open('real_system_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Report saved to: real_system_test_report.json")
    
    return summary['success_rate'] > 0.7

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 SYSTEM PASSES REAL TESTING!")
    else:
        print("\n⚠️ SYSTEM NEEDS ATTENTION")
