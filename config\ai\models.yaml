# LOCAL-ONLY AI MODEL CONFIGURATION
# All API providers disabled - using only local Ollama models

api_models: []
cache_enabled: true
cache_ttl: 3600
confidence_threshold: 0.7
cost_tracking_enabled: false  # No costs for local models
daily_cost_limit: 0.0
enable_fine_tuning: true
ensemble_voting: true
fallback_order:
- ollama
- local

# LOCAL MODEL PROVIDERS ONLY
llm_providers:
  # Primary Ollama Provider
  ollama:
    enabled: true
    provider_type: ollama
    base_url: "http://localhost:11434"
    api_key: null
    context_window: 8192
    cost_per_1k_tokens:
      input: 0.0
      output: 0.0
    gpu_enabled: true
    max_tokens: 2000
    temperature: 0.1
    timeout: 60
    retry_attempts: 3
    supports_streaming: true
    supports_function_calling: false
    rate_limit:
      requests_per_minute: 1000
    priority: 1

  # Fallback Local Provider
  local:
    enabled: true
    provider_type: local
    base_url: null
    api_key: null
    context_window: 4096
    cost_per_1k_tokens:
      input: 0.0
      output: 0.0
    gpu_enabled: false
    max_tokens: 2000
    model: qwen3-8b
    model_path: qwen3/Qwen3-8B-Q4_K_M.gguf
    priority: 2
    quantization: Q4_K_M
    rate_limit:
      requests_per_minute: 60
    retry_attempts: 3
    supports_function_calling: false
    supports_streaming: true
    temperature: 0.1
    timeout: 15

# LOCAL CONFIGURATION
local_model_name: qwen3-8b
local_model_path: qwen3/Qwen3-8B-Q4_K_M.gguf
max_concurrent_requests: 5  # Reduced for local processing
request_timeout: 120  # Increased for local models
