#!/usr/bin/env python3
"""
EMERGENCY SYSTEM FIX - FIXING ALL ISSUES NOW
No excuses, no lies - making every model work properly
"""

import asyncio
import time
import subprocess
import json
from typing import Dict, List, Any
from ultimate_ensemble_system import UltimateEnsembleSystem

class EmergencySystemFixer:
    """Emergency fixer - making everything work properly"""
    
    def __init__(self):
        self.system = UltimateEnsembleSystem()
        self.working_models = []
        self.broken_models = []
        
    async def emergency_fix_all(self) -> Dict[str, Any]:
        """Emergency fix - make everything work or die trying"""
        print("🚨 EMERGENCY SYSTEM FIX - FIXING ALL ISSUES NOW")
        print("=" * 70)
        print("🔥 NO EXCUSES - MAKING EVERY MODEL WORK")
        print()
        
        # Step 1: Test every single model individually
        await self._test_every_model()
        
        # Step 2: Fix broken models
        await self._fix_broken_models()
        
        # Step 3: Optimize working models
        await self._optimize_working_models()
        
        # Step 4: Test all modes again
        results = await self._test_all_modes_final()
        
        return results
    
    async def _test_every_model(self):
        """Test every single model individually"""
        print("🧪 TESTING EVERY MODEL INDIVIDUALLY...")
        print("-" * 50)
        
        for model_name, config in self.system.models.items():
            print(f"Testing {model_name}...")
            
            try:
                # Test with very simple prompt
                start_time = time.time()
                
                # Direct Ollama call to test model
                result = await self._test_model_direct(model_name)
                
                test_time = time.time() - start_time
                
                if result['success'] and test_time < 15:
                    self.working_models.append(model_name)
                    config['reliable'] = True
                    config['avg_response_time'] = test_time
                    print(f"   ✅ WORKING - {test_time:.1f}s")
                else:
                    self.broken_models.append(model_name)
                    config['reliable'] = False
                    print(f"   ❌ BROKEN - {result.get('error', 'timeout')}")
                    
            except Exception as e:
                self.broken_models.append(model_name)
                config['reliable'] = False
                print(f"   ❌ BROKEN - {e}")
        
        print(f"\n📊 MODEL TEST RESULTS:")
        print(f"   ✅ Working: {len(self.working_models)}")
        print(f"   ❌ Broken: {len(self.broken_models)}")
        print(f"   Working models: {self.working_models}")
        print(f"   Broken models: {self.broken_models}")
    
    async def _test_model_direct(self, model_name: str) -> Dict[str, Any]:
        """Test model with direct Ollama call"""
        try:
            # Use subprocess for direct control
            cmd = [
                'ollama', 'run', model_name,
                'Quick test. Respond with: ACTION: BUY, CONFIDENCE: 0.8'
            ]
            
            # Run with timeout
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=12.0  # 12 second timeout
                )
                
                if process.returncode == 0 and stdout:
                    response = stdout.decode().strip()
                    if len(response) > 10:  # Got actual response
                        return {'success': True, 'response': response}
                    else:
                        return {'success': False, 'error': 'Empty response'}
                else:
                    error = stderr.decode() if stderr else 'Process failed'
                    return {'success': False, 'error': error}
                    
            except asyncio.TimeoutError:
                process.kill()
                return {'success': False, 'error': 'Timeout'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _fix_broken_models(self):
        """Fix broken models"""
        print(f"\n🔧 FIXING BROKEN MODELS...")
        print("-" * 50)
        
        if not self.broken_models:
            print("   ✅ No broken models to fix!")
            return
        
        for model_name in self.broken_models:
            print(f"Fixing {model_name}...")
            
            try:
                # Try to restart/reload the model
                print(f"   🔄 Restarting model...")
                
                # Stop model
                stop_cmd = ['ollama', 'stop', model_name]
                await asyncio.create_subprocess_exec(*stop_cmd)
                await asyncio.sleep(2)
                
                # Test again
                result = await self._test_model_direct(model_name)
                
                if result['success']:
                    self.working_models.append(model_name)
                    self.broken_models.remove(model_name)
                    self.system.models[model_name]['reliable'] = True
                    print(f"   ✅ FIXED!")
                else:
                    print(f"   ❌ Still broken: {result.get('error', 'unknown')}")
                    # Mark as backup only
                    self.system.models[model_name]['backup_only'] = True
                    self.system.models[model_name]['weight'] = 0.01
                    
            except Exception as e:
                print(f"   ❌ Fix failed: {e}")
    
    async def _optimize_working_models(self):
        """Optimize working models for maximum performance"""
        print(f"\n⚡ OPTIMIZING WORKING MODELS...")
        print("-" * 50)
        
        if len(self.working_models) < 2:
            print("   ⚠️ WARNING: Only have {len(self.working_models)} working models!")
        
        # Sort working models by performance
        working_with_times = []
        for model_name in self.working_models:
            config = self.system.models[model_name]
            avg_time = config.get('avg_response_time', 999)
            working_with_times.append((model_name, avg_time))
        
        # Sort by speed (fastest first)
        working_with_times.sort(key=lambda x: x[1])
        
        # Assign optimized tiers
        for i, (model_name, avg_time) in enumerate(working_with_times):
            config = self.system.models[model_name]
            
            if i < 2:  # Top 2 fastest
                config['tier'] = 'lightning'
                config['timeout'] = 10
                config['weight'] = 0.4
                print(f"   ⚡ {model_name}: LIGHTNING tier ({avg_time:.1f}s)")
            elif i < 4:  # Next 2 fastest
                config['tier'] = 'fast'
                config['timeout'] = 15
                config['weight'] = 0.3
                print(f"   🚀 {model_name}: FAST tier ({avg_time:.1f}s)")
            else:  # Remaining
                config['tier'] = 'powerful'
                config['timeout'] = 20
                config['weight'] = 0.2
                print(f"   💪 {model_name}: POWERFUL tier ({avg_time:.1f}s)")
        
        # Update system timeouts
        self.system.timeout_base = 8  # Very aggressive
        print(f"   🎯 Base timeout: {self.system.timeout_base}s")
    
    async def _test_all_modes_final(self) -> Dict[str, Any]:
        """Final test of all modes"""
        print(f"\n🧪 FINAL TEST - ALL MODES...")
        print("-" * 50)
        
        results = {}
        
        # Test Emergency Mode
        print(f"\n⚡ EMERGENCY MODE:")
        try:
            start_time = time.time()
            decision = await self.system.get_ultimate_trading_decision(
                "AAPL", {"trend": "bullish"}, "emergency"
            )
            emergency_time = time.time() - start_time
            
            emergency_success = (
                emergency_time < 15 and 
                hasattr(decision, 'final_action') and
                decision.models_used > 0  # Must have real model responses
            )
            
            results['emergency'] = {
                'success': emergency_success,
                'time': emergency_time,
                'action': getattr(decision, 'final_action', 'NONE'),
                'confidence': getattr(decision, 'ensemble_confidence', 0),
                'models_used': getattr(decision, 'models_used', 0),
                'real_responses': getattr(decision, 'models_used', 0) > 0
            }
            
            status = "✅ PERFECT" if emergency_success else "❌ FAILED"
            print(f"   {status}: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {emergency_time:.1f}s - {decision.models_used} models")
            
        except Exception as e:
            results['emergency'] = {'success': False, 'error': str(e)}
            print(f"   ❌ FAILED: {e}")
        
        # Test Fast Mode
        print(f"\n🚀 FAST MODE:")
        try:
            start_time = time.time()
            decision = await self.system.get_ultimate_trading_decision(
                "TSLA", {"trend": "bearish"}, "fast"
            )
            fast_time = time.time() - start_time
            
            fast_success = (
                fast_time < 20 and 
                hasattr(decision, 'final_action') and
                decision.models_used > 0
            )
            
            results['fast'] = {
                'success': fast_success,
                'time': fast_time,
                'action': getattr(decision, 'final_action', 'NONE'),
                'confidence': getattr(decision, 'ensemble_confidence', 0),
                'models_used': getattr(decision, 'models_used', 0),
                'real_responses': getattr(decision, 'models_used', 0) > 0
            }
            
            status = "✅ PERFECT" if fast_success else "❌ FAILED"
            print(f"   {status}: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {fast_time:.1f}s - {decision.models_used} models")
            
        except Exception as e:
            results['fast'] = {'success': False, 'error': str(e)}
            print(f"   ❌ FAILED: {e}")
        
        # Test Normal Mode
        print(f"\n📊 NORMAL MODE:")
        try:
            start_time = time.time()
            decision = await self.system.get_ultimate_trading_decision(
                "BTC", {"trend": "volatile"}, "normal"
            )
            normal_time = time.time() - start_time
            
            normal_success = (
                normal_time < 25 and 
                hasattr(decision, 'final_action') and
                decision.models_used > 1  # Require multiple models
            )
            
            results['normal'] = {
                'success': normal_success,
                'time': normal_time,
                'action': getattr(decision, 'final_action', 'NONE'),
                'confidence': getattr(decision, 'ensemble_confidence', 0),
                'models_used': getattr(decision, 'models_used', 0),
                'real_responses': getattr(decision, 'models_used', 0) > 1
            }
            
            status = "✅ PERFECT" if normal_success else "❌ FAILED"
            print(f"   {status}: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {normal_time:.1f}s - {decision.models_used} models")
            
        except Exception as e:
            results['normal'] = {'success': False, 'error': str(e)}
            print(f"   ❌ FAILED: {e}")
        
        # Test Comprehensive Mode
        print(f"\n💪 COMPREHENSIVE MODE:")
        try:
            start_time = time.time()
            decision = await self.system.get_ultimate_trading_decision(
                "NVDA", {"trend": "bullish"}, "comprehensive"
            )
            comp_time = time.time() - start_time
            
            comp_success = (
                comp_time < 30 and 
                hasattr(decision, 'final_action') and
                decision.models_used > 2  # Require multiple models
            )
            
            results['comprehensive'] = {
                'success': comp_success,
                'time': comp_time,
                'action': getattr(decision, 'final_action', 'NONE'),
                'confidence': getattr(decision, 'ensemble_confidence', 0),
                'models_used': getattr(decision, 'models_used', 0),
                'real_responses': getattr(decision, 'models_used', 0) > 2
            }
            
            status = "✅ PERFECT" if comp_success else "❌ FAILED"
            print(f"   {status}: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {comp_time:.1f}s - {decision.models_used} models")
            
        except Exception as e:
            results['comprehensive'] = {'success': False, 'error': str(e)}
            print(f"   ❌ FAILED: {e}")
        
        # Calculate final results
        successful_modes = sum(1 for mode_result in results.values() if mode_result.get('success', False))
        total_modes = len(results)
        success_rate = successful_modes / total_modes
        
        print(f"\n🎯 FINAL EMERGENCY FIX RESULTS:")
        print(f"   Working Models: {len(self.working_models)}")
        print(f"   Broken Models: {len(self.broken_models)}")
        print(f"   Successful Modes: {successful_modes}/{total_modes}")
        print(f"   Success Rate: {success_rate:.1%}")
        
        if success_rate == 1.0:
            print(f"\n🎉 EMERGENCY FIX COMPLETE - ALL ISSUES RESOLVED!")
            print(f"🔥 SYSTEM IS NOW PERFECT!")
        elif success_rate >= 0.75:
            print(f"\n✅ MAJOR IMPROVEMENTS - MOST ISSUES FIXED!")
        else:
            print(f"\n⚠️ STILL HAVE ISSUES - NEED MORE AGGRESSIVE FIXES!")
        
        results['summary'] = {
            'working_models': len(self.working_models),
            'broken_models': len(self.broken_models),
            'successful_modes': successful_modes,
            'total_modes': total_modes,
            'success_rate': success_rate,
            'all_perfect': success_rate == 1.0
        }
        
        return results

async def main():
    """Run emergency fix"""
    fixer = EmergencySystemFixer()
    results = await fixer.emergency_fix_all()
    
    # Save results
    with open('emergency_fix_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: emergency_fix_results.json")
    
    if results['summary']['all_perfect']:
        print(f"\n🚀 MISSION ACCOMPLISHED - ALL ISSUES FIXED!")
        return True
    else:
        print(f"\n🔧 MISSION INCOMPLETE - NEED MORE WORK!")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    if not success:
        print(f"\n💀 EMERGENCY FIX FAILED - SYSTEM STILL HAS ISSUES!")
    else:
        print(f"\n🎉 EMERGENCY FIX SUCCESS - SYSTEM IS PERFECT!")
