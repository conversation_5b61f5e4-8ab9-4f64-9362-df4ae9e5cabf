#!/usr/bin/env python3
"""
Enhanced Local Model Caller - Robust Ollama integration with comprehensive error handling
"""

import subprocess
import time
import os
import json
import asyncio
import aiohttp
import logging
from typing import Optional, Tuple, Dict, Any, List
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelStatus(Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    ERROR = "error"
    TIMEOUT = "timeout"
    UNKNOWN = "unknown"

@dataclass
class ModelResponse:
    success: bool
    response: str
    response_time: float
    model_name: str
    status: ModelStatus
    error_message: Optional[str] = None
    confidence: Optional[float] = None

class EnhancedLocalModelCaller:
    def __init__(self):
        # Set proper encoding
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'

        self.ollama_url = "http://localhost:11434"
        self.model_status = {}
        self.model_performance = {}
        self.session = None

    async def get_available_models(self) -> List[str]:
        """Get list of available Ollama models"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(f"{self.ollama_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    return [model['name'] for model in data.get('models', [])]
                else:
                    logger.error(f"Failed to get models: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"Error getting models: {e}")
            # Fallback to subprocess
            try:
                result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # Skip header
                    return [line.split()[0] for line in lines if line.strip()]
                return []
            except Exception:
                return []

    def call_model_safe(self, model_name: str, prompt: str, timeout: int = 60) -> ModelResponse:
        """Safely call a model with comprehensive error handling"""
        start_time = time.time()

        try:
            # Check if model is available
            if model_name not in self.model_status:
                self.model_status[model_name] = ModelStatus.UNKNOWN

            # Execute model call
            result = subprocess.run([
                'ollama', 'run', model_name, prompt
            ],
            capture_output=True,
            text=True,
            timeout=timeout,
            encoding='utf-8',
            errors='replace'
            )

            response_time = time.time() - start_time

            if result.returncode == 0:
                response = result.stdout.strip()
                self.model_status[model_name] = ModelStatus.AVAILABLE
                self._update_performance(model_name, response_time, True)

                return ModelResponse(
                    success=True,
                    response=response,
                    response_time=response_time,
                    model_name=model_name,
                    status=ModelStatus.AVAILABLE
                )
            else:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                self.model_status[model_name] = ModelStatus.ERROR
                self._update_performance(model_name, response_time, False)

                return ModelResponse(
                    success=False,
                    response="",
                    response_time=response_time,
                    model_name=model_name,
                    status=ModelStatus.ERROR,
                    error_message=f"Model error: {error_msg}"
                )

        except subprocess.TimeoutExpired:
            response_time = time.time() - start_time
            self.model_status[model_name] = ModelStatus.TIMEOUT
            self._update_performance(model_name, response_time, False)

            return ModelResponse(
                success=False,
                response="",
                response_time=response_time,
                model_name=model_name,
                status=ModelStatus.TIMEOUT,
                error_message=f"Timeout after {timeout}s"
            )
        except Exception as e:
            response_time = time.time() - start_time
            self.model_status[model_name] = ModelStatus.ERROR
            self._update_performance(model_name, response_time, False)

            return ModelResponse(
                success=False,
                response="",
                response_time=response_time,
                model_name=model_name,
                status=ModelStatus.ERROR,
                error_message=f"Exception: {str(e)}"
            )

    def call_model_with_retries(self, model_name: str, prompt: str, max_retries: int = 2) -> ModelResponse:
        """Call model with automatic retries"""
        last_response = None

        for attempt in range(max_retries + 1):
            response = self.call_model_safe(model_name, prompt)
            last_response = response

            if response.success:
                return response

            if attempt < max_retries:
                logger.info(f"Retry {attempt + 1}/{max_retries} for {model_name}: {response.error_message}")
                time.sleep(2)  # Brief pause between retries

        return last_response

    def _update_performance(self, model_name: str, response_time: float, success: bool):
        """Update model performance metrics"""
        if model_name not in self.model_performance:
            self.model_performance[model_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'success_rate': 0.0
            }

        perf = self.model_performance[model_name]
        perf['total_calls'] += 1
        perf['total_time'] += response_time
        perf['avg_time'] = perf['total_time'] / perf['total_calls']

        if success:
            perf['successful_calls'] += 1

        perf['success_rate'] = perf['successful_calls'] / perf['total_calls']

    def get_model_performance(self, model_name: str) -> Dict[str, Any]:
        """Get performance metrics for a model"""
        return self.model_performance.get(model_name, {})

    def get_best_models(self, limit: int = 5) -> List[str]:
        """Get best performing models based on success rate and speed"""
        if not self.model_performance:
            return []

        # Sort by success rate (desc) then by avg time (asc)
        sorted_models = sorted(
            self.model_performance.items(),
            key=lambda x: (x[1]['success_rate'], -x[1]['avg_time']),
            reverse=True
        )

        return [model for model, _ in sorted_models[:limit]]

    async def close(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()

# Backward compatibility class
class OptimizedModelCaller:
    def __init__(self):
        self.enhanced_caller = EnhancedLocalModelCaller()

    def call_model_safe(self, model_name: str, prompt: str, timeout: int = 45) -> Tuple[bool, str, float]:
        """Legacy method for backward compatibility"""
        response = self.enhanced_caller.call_model_safe(model_name, prompt, timeout)
        return response.success, response.response, response.response_time

    def call_model_with_retries(self, model_name: str, prompt: str, max_retries: int = 2) -> Tuple[bool, str, float]:
        """Legacy method for backward compatibility"""
        response = self.enhanced_caller.call_model_with_retries(model_name, prompt, max_retries)
        return response.success, response.response, response.response_time

# Global instances
enhanced_model_caller = EnhancedLocalModelCaller()
model_caller = OptimizedModelCaller()  # For backward compatibility
