#!/usr/bin/env python3
"""
ULTIMATE ENSEMBLE TRADING SYSTEM
Blazing fast, rock-solid reliable, ready for everything
"""

import asyncio
import concurrent.futures
import time
import yaml
import re
import json
import hashlib
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
import logging
from pathlib import Path
import multiprocessing as mp

# Configure high-performance logging (ASCII only for Windows compatibility)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ultimate_ensemble.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class UltimateModelResponse:
    """Enhanced model response with performance metrics"""
    success: bool
    response: str
    response_time: float
    model_name: str
    confidence: float = 0.0
    action: str = "HOLD"
    reasoning: str = ""
    error_message: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class TradingDecision:
    """Ultimate trading decision with comprehensive data"""
    symbol: str
    final_action: str
    ensemble_confidence: float
    consensus_level: float
    models_used: int
    response_time: float
    individual_predictions: List[Dict[str, Any]]
    voting_strategies: Dict[str, Any]
    market_context: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    timestamp: datetime
    cache_hit: bool = False
    meta_voting: bool = True

class UltimateEnsembleSystem:
    """Ultimate Ensemble Trading System - Optimized for maximum performance and reliability"""
    
    def __init__(self, config_path: str = "config/ensemble_config.yaml"):
        logger.info("Initializing Ultimate Ensemble Trading System...")
        
        # Performance settings (initialize first)
        self.max_workers = min(16, mp.cpu_count() * 2)  # Aggressive parallelization
        self.timeout_base = 25  # Reduced base timeout
        self.cache_ttl = 180  # 3 minutes for faster updates
        self.max_retries = 1  # Faster failure recovery

        # Core configuration (after performance settings)
        self.config_path = config_path
        self.config = self._load_enhanced_config()
        
        # Enhanced models with performance tiers
        self.models = self._load_performance_optimized_models()
        self.model_performance = {}
        self.prediction_cache = {}
        self.cache_lock = threading.RLock()
        self.decision_history = deque(maxlen=5000)
        
        # Advanced features
        self.adaptive_learning = True
        self.risk_integration = True
        self.market_regime_detection = True
        
        # Initialize performance tracking
        self._initialize_performance_system()
        
        logger.info(f"✅ Ultimate Ensemble initialized:")
        logger.info(f"   🤖 Models: {len(self.models)} high-performance models")
        logger.info(f"   ⚡ Max workers: {self.max_workers}")
        logger.info(f"   🎯 Base timeout: {self.timeout_base}s")
        logger.info(f"   🧠 Adaptive learning: {self.adaptive_learning}")
    
    def _load_enhanced_config(self) -> Dict[str, Any]:
        """Load enhanced configuration with performance optimizations"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"Config load failed: {e}, using optimized defaults")
            config = {}
        
        # Enhanced defaults for maximum performance
        ensemble_config = config.get('ensemble', {})
        ensemble_config.update({
            'max_parallel_models': self.max_workers,
            'timeout_per_model': self.timeout_base,
            'cache_enabled': True,
            'cache_ttl': self.cache_ttl,
            'adaptive_timeouts': True,
            'performance_tracking': True,
            'risk_integration': True,
            'market_regime_detection': True,
            'confidence_threshold': 0.6,  # Lower for more decisions
            'consensus_threshold': 0.5,   # Lower for faster consensus
        })
        
        config['ensemble'] = ensemble_config
        return config
    
    def _load_performance_optimized_models(self) -> Dict[str, Dict[str, Any]]:
        """Load models optimized for performance tiers"""
        
        # TIER 1: Lightning Fast Models (< 20s)
        tier1_models = {
            "unrestricted-noryon-deepscaler-finance-v2-latest:latest": {
                'specialization': 'scalability_analysis',
                'weight': 0.20,
                'confidence_threshold': 0.60,
                'priority': 1,
                'timeout': 15,
                'tier': 'lightning'
            },
            "unrestricted-noryon-falcon3-finance-v1-latest:latest": {
                'specialization': 'momentum_analysis',
                'weight': 0.18,
                'confidence_threshold': 0.60,
                'priority': 2,
                'timeout': 18,
                'tier': 'lightning'
            },
            "unrestricted-noryon-cogito-finance-v2-latest:latest": {
                'specialization': 'pattern_recognition',
                'weight': 0.16,
                'confidence_threshold': 0.60,
                'priority': 3,
                'timeout': 20,
                'tier': 'lightning'
            }
        }
        
        # TIER 2: Fast Models (20-35s)
        tier2_models = {
            "marco-o1:latest": {
                'specialization': 'reasoning_analysis',
                'weight': 0.15,
                'confidence_threshold': 0.70,
                'priority': 4,
                'timeout': 25,
                'tier': 'fast'
            },
            "unrestricted-noryon-phi-4-9b-finance-latest:latest": {
                'specialization': 'technical_analysis',
                'weight': 0.14,
                'confidence_threshold': 0.65,
                'priority': 5,
                'timeout': 30,
                'tier': 'fast'
            },
            "unrestricted-noryon-exaone-deep-finance-v2-latest:latest": {
                'specialization': 'deep_analysis',
                'weight': 0.12,
                'confidence_threshold': 0.65,
                'priority': 6,
                'timeout': 35,
                'tier': 'fast'
            }
        }
        
        # TIER 3: Powerful Models (35-50s) - Used selectively
        tier3_models = {
            "unrestricted-noryon-deepseek-r1-finance-v2-latest:latest": {
                'specialization': 'risk_assessment',
                'weight': 0.10,
                'confidence_threshold': 0.70,
                'priority': 7,
                'timeout': 40,
                'tier': 'powerful',
                'selective_use': True  # Only for high-stakes decisions
            },
            "unrestricted-noryon-qwen3-finance-v2-latest:latest": {
                'specialization': 'market_analysis',
                'weight': 0.08,
                'confidence_threshold': 0.65,
                'priority': 8,
                'timeout': 45,
                'tier': 'powerful',
                'selective_use': True
            },
            "unrestricted-noryon-gemma-3-12b-finance-latest:latest": {
                'specialization': 'fundamental_analysis',
                'weight': 0.07,
                'confidence_threshold': 0.65,
                'priority': 9,
                'timeout': 50,
                'tier': 'powerful',
                'selective_use': True
            }
        }
        
        # Combine all tiers
        all_models = {**tier1_models, **tier2_models, **tier3_models}
        
        # Add common properties
        for model_name, config in all_models.items():
            config.update({
                'type': 'ollama',
                'enabled': True,
                'max_retries': self.max_retries
            })
        
        logger.info(f"📊 Model Performance Tiers:")
        logger.info(f"   ⚡ Lightning (< 20s): {len(tier1_models)} models")
        logger.info(f"   🚀 Fast (20-35s): {len(tier2_models)} models")
        logger.info(f"   💪 Powerful (35-50s): {len(tier3_models)} models")
        
        return all_models
    
    def _initialize_performance_system(self):
        """Initialize advanced performance tracking"""
        for model_name, config in self.models.items():
            self.model_performance[model_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'avg_response_time': config['timeout'] * 0.7,  # Optimistic estimate
                'success_rate': 0.8,  # Optimistic start
                'confidence_scores': [],
                'tier': config['tier'],
                'last_updated': datetime.now(),
                'performance_score': 0.5
            }
    
    async def get_ultimate_trading_decision(self, symbol: str, market_context: Dict[str, Any] = None, 
                                          urgency: str = "normal") -> TradingDecision:
        """Get ultimate trading decision with adaptive model selection"""
        start_time = time.time()
        
        logger.info(f"🎯 Ultimate Trading Analysis: {symbol} (Urgency: {urgency})")
        
        # Check cache first
        cache_key = self._generate_cache_key(symbol, market_context, urgency)
        cached_decision = self._get_cached_decision(cache_key)
        if cached_decision:
            logger.info(f"⚡ Cache hit! Response time: 0.001s")
            return cached_decision
        
        # Select models based on urgency and performance
        selected_models = self._select_optimal_models(urgency)
        logger.info(f"🤖 Selected {len(selected_models)} models for {urgency} urgency")
        
        # Enhanced prompt generation
        prompt = self._create_ultimate_prompt(symbol, market_context, urgency)
        
        # Execute parallel predictions with performance optimization
        predictions = await self._execute_ultra_fast_predictions(prompt, selected_models)
        
        # BULLETPROOF: Filter and validate predictions with relaxed requirements
        valid_predictions = [p for p in predictions if p.success and p.confidence > 0.1]  # Lowered threshold

        if len(valid_predictions) >= 1:
            # Use valid predictions for voting
            logger.info(f"🎯 Using {len(valid_predictions)} valid predictions for voting")
            decision = self._apply_ultimate_voting(symbol, valid_predictions, market_context, start_time)
        elif predictions:
            # BULLETPROOF: Use all predictions (including failed ones) for emergency decision
            logger.warning(f"⚠️ No valid predictions, using emergency decision with {len(predictions)} total predictions")
            decision = self._create_emergency_decision(symbol, predictions, start_time)
        else:
            # BULLETPROOF: No predictions at all - create fallback decision
            logger.error(f"🚨 No predictions available, creating fallback decision")
            decision = self._create_emergency_decision(symbol, [], start_time)
        
        # Cache the decision
        self._cache_decision(cache_key, decision)
        
        # Update performance metrics
        self._update_performance_metrics(predictions)
        
        # Store in history
        self.decision_history.append(decision)
        
        logger.info(f"🎯 ULTIMATE DECISION COMPLETE:")
        logger.info(f"   Symbol: {symbol}")
        logger.info(f"   Action: {decision.final_action}")
        logger.info(f"   Confidence: {decision.ensemble_confidence:.2f}")
        logger.info(f"   Consensus: {decision.consensus_level:.2f}")
        logger.info(f"   Models: {decision.models_used}")
        logger.info(f"   Time: {decision.response_time:.1f}s")
        
        return decision

    def _select_optimal_models(self, urgency: str) -> List[Tuple[str, Dict[str, Any]]]:
        """Select optimal models based on urgency and performance"""

        if urgency == "emergency":
            # Only lightning-fast models
            selected = [(name, config) for name, config in self.models.items()
                       if config['tier'] == 'lightning']
            logger.info("⚡ Emergency mode: Lightning models only")

        elif urgency == "fast":
            # Lightning + fast models
            selected = [(name, config) for name, config in self.models.items()
                       if config['tier'] in ['lightning', 'fast']]
            logger.info("🚀 Fast mode: Lightning + Fast models")

        else:  # normal or comprehensive
            # All models, but prioritize by performance
            all_models = list(self.models.items())

            # Sort by performance score and tier
            def sort_key(item):
                name, config = item
                perf = self.model_performance.get(name, {})
                tier_priority = {'lightning': 3, 'fast': 2, 'powerful': 1}
                return (
                    tier_priority.get(config['tier'], 0),
                    perf.get('performance_score', 0.5),
                    -perf.get('avg_response_time', 60)
                )

            selected = sorted(all_models, key=sort_key, reverse=True)

            # Limit based on urgency
            if urgency == "normal":
                selected = selected[:8]  # Top 8 models
            else:  # comprehensive
                selected = selected[:12]  # More models for comprehensive analysis

        return selected

    def _create_ultimate_prompt(self, symbol: str, market_context: Dict[str, Any], urgency: str) -> str:
        """Create optimized prompt for maximum performance"""

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Urgency-specific instructions
        urgency_instructions = {
            "emergency": "URGENT: Provide immediate trading decision. Focus on risk and momentum.",
            "fast": "FAST: Quick analysis required. Focus on key indicators and trends.",
            "normal": "STANDARD: Comprehensive analysis with technical and fundamental factors.",
            "comprehensive": "DETAILED: Full analysis including all market factors and risks."
        }

        base_prompt = f"""
ULTIMATE TRADING ANALYSIS
Symbol: {symbol}
Timestamp: {timestamp}
Urgency: {urgency.upper()}

{urgency_instructions.get(urgency, urgency_instructions['normal'])}

REQUIRED OUTPUT FORMAT:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation]

MARKET CONTEXT:"""

        if market_context:
            for key, value in market_context.items():
                base_prompt += f"\n- {key.replace('_', ' ').title()}: {value}"
        else:
            base_prompt += "\n- Real-time market analysis required"

        base_prompt += f"""

ANALYSIS REQUIREMENTS:
1. Price action and momentum
2. Risk/reward ratio
3. Market sentiment
4. Entry/exit timing

RESPOND EXACTLY AS:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [Your analysis in 1-2 sentences]
"""

        return base_prompt

    async def _execute_ultra_fast_predictions(self, prompt: str, selected_models: List[Tuple[str, Dict[str, Any]]]) -> List[UltimateModelResponse]:
        """Execute predictions with maximum speed optimization"""

        predictions = []

        # Use ProcessPoolExecutor for true parallelism
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_model = {}

            for model_name, model_config in selected_models:
                # Adaptive timeout based on tier and performance
                timeout = self._get_adaptive_timeout(model_name, model_config)

                future = executor.submit(
                    self._call_model_ultra_fast,
                    model_name,
                    prompt,
                    timeout,
                    model_config
                )
                future_to_model[future] = (model_name, model_config)

            # BULLETPROOF: Collect results with graceful timeout handling
            try:
                completed_futures = concurrent.futures.as_completed(
                    future_to_model,
                    timeout=min(60, max(20, len(selected_models) * 5))
                )

                for future in completed_futures:
                    model_name, model_config = future_to_model[future]
                    try:
                        result = future.result(timeout=5)  # Individual result timeout
                        predictions.append(result)

                    except concurrent.futures.TimeoutError:
                        logger.warning(f"⏰ {model_name} timed out")
                        predictions.append(UltimateModelResponse(
                            success=False,
                            response="",
                            response_time=model_config['timeout'],
                            model_name=model_name,
                            error_message="Timeout"
                        ))

                    except Exception as e:
                        logger.error(f"❌ {model_name} failed: {e}")
                        predictions.append(UltimateModelResponse(
                            success=False,
                            response="",
                            response_time=0.0,
                            model_name=model_name,
                            error_message=str(e)
                        ))

            except concurrent.futures.TimeoutError:
                # BULLETPROOF: Handle overall timeout gracefully
                logger.warning(f"⚠️ Overall timeout reached, using {len(predictions)} completed predictions")

                # Add timeout entries for remaining futures
                for future, (model_name, model_config) in future_to_model.items():
                    if not future.done():
                        predictions.append(UltimateModelResponse(
                            success=False,
                            response="",
                            response_time=model_config['timeout'],
                            model_name=model_name,
                            error_message="Overall timeout"
                        ))

            except Exception as e:
                logger.error(f"❌ Prediction execution error: {e}")
                # Add failed entries for all models
                for future, (model_name, model_config) in future_to_model.items():
                    if not future.done():
                        predictions.append(UltimateModelResponse(
                            success=False,
                            response="",
                            response_time=0.0,
                            model_name=model_name,
                            error_message=f"Execution error: {e}"
                        ))

        # Log performance summary
        successful = len([p for p in predictions if p.success])
        avg_time = sum(p.response_time for p in predictions if p.success) / max(1, successful)

        logger.info(f"📊 Prediction Summary: {successful}/{len(predictions)} successful, avg {avg_time:.1f}s")

        return predictions

    def _call_model_ultra_fast(self, model_name: str, prompt: str, timeout: int, model_config: Dict[str, Any]) -> UltimateModelResponse:
        """Ultra-fast model calling with optimizations"""
        start_time = time.time()

        try:
            # Import here to avoid circular imports
            from optimized_model_caller import EnhancedLocalModelCaller

            caller = EnhancedLocalModelCaller()
            response = caller.call_model_safe(model_name, prompt, timeout)

            if response.success:
                # Parse response immediately
                action, confidence, reasoning = self._parse_response_fast(response.response)

                return UltimateModelResponse(
                    success=True,
                    response=response.response,
                    response_time=time.time() - start_time,
                    model_name=model_name,
                    confidence=confidence,
                    action=action,
                    reasoning=reasoning
                )
            else:
                return UltimateModelResponse(
                    success=False,
                    response=response.response,
                    response_time=time.time() - start_time,
                    model_name=model_name,
                    error_message=response.error_message
                )

        except Exception as e:
            return UltimateModelResponse(
                success=False,
                response="",
                response_time=time.time() - start_time,
                model_name=model_name,
                error_message=str(e)
            )

    def _parse_response_fast(self, response: str) -> Tuple[str, float, str]:
        """Fast response parsing with fallbacks"""
        try:
            response_upper = response.upper()

            # Extract action
            action = "HOLD"  # Default
            if "ACTION:" in response_upper:
                action_match = re.search(r'ACTION:\s*([A-Z]+)', response_upper)
                if action_match:
                    action = action_match.group(1)
            elif "BUY" in response_upper and "SELL" not in response_upper:
                action = "BUY"
            elif "SELL" in response_upper and "BUY" not in response_upper:
                action = "SELL"

            # Extract confidence
            confidence = 0.5  # Default
            conf_patterns = [
                r'CONFIDENCE:\s*([0-9.]+)',
                r'CONF:\s*([0-9.]+)',
                r'([0-9.]+)\s*CONFIDENCE'
            ]

            for pattern in conf_patterns:
                match = re.search(pattern, response_upper)
                if match:
                    conf_value = float(match.group(1))
                    confidence = conf_value if conf_value <= 1.0 else conf_value / 10.0
                    break

            # Extract reasoning (first sentence after REASONING:)
            reasoning = "Market analysis"  # Default
            if "REASONING:" in response_upper:
                reasoning_match = re.search(r'REASONING:\s*([^.]+)', response, re.IGNORECASE)
                if reasoning_match:
                    reasoning = reasoning_match.group(1).strip()[:100]  # Limit length

            return action, confidence, reasoning

        except Exception as e:
            logger.warning(f"Parse error: {e}")
            return "HOLD", 0.5, "Parse error"

    def _get_adaptive_timeout(self, model_name: str, model_config: Dict[str, Any]) -> int:
        """Get adaptive timeout based on performance and tier"""
        base_timeout = model_config['timeout']

        # Get performance data
        perf = self.model_performance.get(model_name, {})
        avg_time = perf.get('avg_response_time', base_timeout)
        success_rate = perf.get('success_rate', 0.8)

        # Adaptive adjustment
        if success_rate > 0.9 and avg_time < base_timeout * 0.8:
            # High performer - reduce timeout
            adaptive_timeout = max(int(avg_time * 1.2), base_timeout // 2)
        elif success_rate < 0.5:
            # Poor performer - increase timeout slightly
            adaptive_timeout = min(int(base_timeout * 1.3), base_timeout + 10)
        else:
            # Normal performer
            adaptive_timeout = base_timeout

        return adaptive_timeout

    def _apply_ultimate_voting(self, symbol: str, predictions: List[UltimateModelResponse],
                              market_context: Dict[str, Any], start_time: float) -> TradingDecision:
        """Apply ultimate voting algorithms with risk assessment"""

        # Convert to structured predictions
        structured_predictions = []
        for pred in predictions:
            structured_predictions.append({
                'model_name': pred.model_name,
                'action': pred.action,
                'confidence': pred.confidence,
                'reasoning': pred.reasoning,
                'response_time': pred.response_time,
                'weight': self.models[pred.model_name]['weight'],
                'tier': self.models[pred.model_name]['tier']
            })

        # Apply multiple voting strategies
        voting_strategies = {
            'tier_weighted': self._tier_weighted_voting(structured_predictions),
            'confidence_squared': self._confidence_squared_voting(structured_predictions),
            'performance_adaptive': self._performance_adaptive_voting(structured_predictions),
            'risk_adjusted': self._risk_adjusted_voting(structured_predictions, market_context)
        }

        # Meta-voting: combine strategies intelligently
        final_decision = self._meta_voting_ultimate(voting_strategies, structured_predictions)

        # Risk assessment
        risk_assessment = self._assess_decision_risk(final_decision, structured_predictions, market_context)

        # Create comprehensive decision object
        decision = TradingDecision(
            symbol=symbol,
            final_action=final_decision['action'],
            ensemble_confidence=final_decision['confidence'],
            consensus_level=final_decision['consensus'],
            models_used=len(structured_predictions),
            response_time=time.time() - start_time,
            individual_predictions=structured_predictions,
            voting_strategies=voting_strategies,
            market_context=market_context or {},
            risk_assessment=risk_assessment,
            timestamp=datetime.now(),
            cache_hit=False,
            meta_voting=True
        )

        return decision

    def _tier_weighted_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Voting weighted by model tier and performance"""
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        tier_multipliers = {'lightning': 1.2, 'fast': 1.0, 'powerful': 0.8}

        for pred in predictions:
            tier_mult = tier_multipliers.get(pred['tier'], 1.0)
            effective_weight = pred['weight'] * tier_mult * pred['confidence']

            action_votes[pred['action']] += effective_weight
            confidence_sum += pred['confidence'] * effective_weight
            total_weight += effective_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'tier_weighted'
        }

    def _confidence_squared_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Voting with squared confidence weighting for high-confidence emphasis"""
        action_votes = defaultdict(float)
        total_weight = 0.0

        for pred in predictions:
            confidence_weight = pred['confidence'] ** 2  # Square for emphasis
            action_votes[pred['action']] += confidence_weight
            total_weight += confidence_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = action_votes[final_action] / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'confidence_squared'
        }

    def _performance_adaptive_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Voting adapted to historical model performance"""
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        for pred in predictions:
            model_perf = self.model_performance.get(pred['model_name'], {})
            success_rate = model_perf.get('success_rate', 0.5)
            perf_score = model_perf.get('performance_score', 0.5)

            # Combine performance metrics
            performance_weight = (success_rate + perf_score) / 2
            effective_weight = pred['weight'] * performance_weight * pred['confidence']

            action_votes[pred['action']] += effective_weight
            confidence_sum += pred['confidence'] * effective_weight
            total_weight += effective_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'performance_adaptive'
        }

    def _risk_adjusted_voting(self, predictions: List[Dict[str, Any]], market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Risk-adjusted voting based on market conditions"""
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        # Risk adjustment factors
        volatility = market_context.get('volatility', 'medium')
        trend = market_context.get('trend', 'neutral')

        risk_adjustments = {
            'BUY': 1.0,
            'SELL': 1.0,
            'HOLD': 1.2  # Favor HOLD in uncertain conditions
        }

        if volatility == 'high':
            risk_adjustments['HOLD'] *= 1.3
        if trend == 'bearish':
            risk_adjustments['SELL'] *= 1.2
            risk_adjustments['BUY'] *= 0.8
        elif trend == 'bullish':
            risk_adjustments['BUY'] *= 1.2
            risk_adjustments['SELL'] *= 0.8

        for pred in predictions:
            risk_mult = risk_adjustments.get(pred['action'], 1.0)
            effective_weight = pred['weight'] * pred['confidence'] * risk_mult

            action_votes[pred['action']] += effective_weight
            confidence_sum += pred['confidence'] * effective_weight
            total_weight += effective_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'risk_adjusted'
        }

    def _meta_voting_ultimate(self, strategies: Dict[str, Dict[str, Any]], predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Ultimate meta-voting combining all strategies"""

        # Weight strategies based on their reliability
        strategy_weights = {
            'tier_weighted': 0.3,
            'confidence_squared': 0.25,
            'performance_adaptive': 0.25,
            'risk_adjusted': 0.2
        }

        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        for strategy_name, result in strategies.items():
            weight = strategy_weights.get(strategy_name, 0.2)
            action_votes[result['action']] += weight * result['confidence']
            confidence_sum += result['confidence'] * weight
            total_weight += weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        # Calculate consensus level
        consensus_level = len([p for p in predictions if p['action'] == final_action]) / len(predictions)

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'consensus': consensus_level
        }

    def _assess_decision_risk(self, decision: Dict[str, Any], predictions: List[Dict[str, Any]],
                             market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive risk assessment for the decision"""

        risk_factors = {
            'consensus_risk': 1.0 - decision['consensus'],
            'confidence_risk': 1.0 - decision['confidence'],
            'model_disagreement': self._calculate_model_disagreement(predictions),
            'market_volatility_risk': self._assess_market_volatility_risk(market_context),
            'timing_risk': self._assess_timing_risk(predictions)
        }

        # Overall risk score (0 = low risk, 1 = high risk)
        overall_risk = sum(risk_factors.values()) / len(risk_factors)

        # Risk level classification
        if overall_risk < 0.3:
            risk_level = "LOW"
        elif overall_risk < 0.6:
            risk_level = "MEDIUM"
        else:
            risk_level = "HIGH"

        return {
            'overall_risk': overall_risk,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'recommendation': self._get_risk_recommendation(overall_risk, decision['action'])
        }

    def _calculate_model_disagreement(self, predictions: List[Dict[str, Any]]) -> float:
        """Calculate disagreement level among models"""
        if len(predictions) < 2:
            return 0.0

        actions = [p['action'] for p in predictions]
        action_counts = defaultdict(int)
        for action in actions:
            action_counts[action] += 1

        # Disagreement is higher when actions are more evenly split
        max_count = max(action_counts.values())
        disagreement = 1.0 - (max_count / len(predictions))

        return disagreement

    def _assess_market_volatility_risk(self, market_context: Dict[str, Any]) -> float:
        """Assess risk based on market volatility"""
        volatility = market_context.get('volatility', 'medium')

        volatility_risk = {
            'low': 0.2,
            'medium': 0.5,
            'high': 0.8,
            'extreme': 1.0
        }

        return volatility_risk.get(volatility, 0.5)

    def _assess_timing_risk(self, predictions: List[Dict[str, Any]]) -> float:
        """Assess timing risk based on response times"""
        if not predictions:
            return 1.0

        avg_response_time = sum(p['response_time'] for p in predictions) / len(predictions)

        # Higher response times indicate potential timing issues
        if avg_response_time > 45:
            return 0.8
        elif avg_response_time > 30:
            return 0.5
        else:
            return 0.2

    def _get_risk_recommendation(self, risk_score: float, action: str) -> str:
        """Get risk-based recommendation"""
        if risk_score > 0.7:
            return f"HIGH RISK: Consider avoiding {action} or reducing position size"
        elif risk_score > 0.4:
            return f"MEDIUM RISK: Proceed with caution on {action}"
        else:
            return f"LOW RISK: {action} appears well-supported"

    # Caching methods
    def _generate_cache_key(self, symbol: str, market_context: Dict[str, Any], urgency: str) -> str:
        """Generate cache key"""
        context_str = json.dumps(market_context or {}, sort_keys=True)
        cache_input = f"{symbol}:{urgency}:{context_str}"
        return hashlib.md5(cache_input.encode()).hexdigest()

    def _get_cached_decision(self, cache_key: str) -> Optional[TradingDecision]:
        """Get cached decision if valid"""
        with self.cache_lock:
            cached = self.prediction_cache.get(cache_key)
            if cached and datetime.now() - cached.timestamp < timedelta(seconds=self.cache_ttl):
                cached.cache_hit = True
                return cached
            elif cached:
                del self.prediction_cache[cache_key]
        return None

    def _cache_decision(self, cache_key: str, decision: TradingDecision):
        """Cache decision"""
        with self.cache_lock:
            self.prediction_cache[cache_key] = decision
            # Cleanup old entries
            if len(self.prediction_cache) > 200:
                oldest_keys = sorted(
                    self.prediction_cache.keys(),
                    key=lambda k: self.prediction_cache[k].timestamp
                )[:100]
                for key in oldest_keys:
                    del self.prediction_cache[key]

    def _update_performance_metrics(self, predictions: List[UltimateModelResponse]):
        """Update performance metrics for all models"""
        for pred in predictions:
            if pred.model_name not in self.model_performance:
                continue

            metrics = self.model_performance[pred.model_name]
            metrics['total_calls'] += 1
            metrics['last_updated'] = datetime.now()

            if pred.success:
                metrics['successful_calls'] += 1
                metrics['avg_response_time'] = (
                    (metrics['avg_response_time'] * (metrics['successful_calls'] - 1) + pred.response_time) /
                    metrics['successful_calls']
                )

                if pred.confidence > 0:
                    metrics['confidence_scores'].append(pred.confidence)
                    if len(metrics['confidence_scores']) > 100:
                        metrics['confidence_scores'] = metrics['confidence_scores'][-50:]

            # Update success rate and performance score
            metrics['success_rate'] = metrics['successful_calls'] / metrics['total_calls']

            # Performance score combines success rate, speed, and confidence
            avg_confidence = sum(metrics['confidence_scores']) / len(metrics['confidence_scores']) if metrics['confidence_scores'] else 0.5
            speed_score = max(0, 1.0 - (metrics['avg_response_time'] / 60.0))  # Normalize to 60s max

            metrics['performance_score'] = (
                metrics['success_rate'] * 0.4 +
                speed_score * 0.3 +
                avg_confidence * 0.3
            )

    def _create_emergency_decision(self, symbol: str, predictions: List[UltimateModelResponse], start_time: float) -> TradingDecision:
        """Create emergency decision when insufficient models respond"""

        if predictions:
            # Use best available prediction
            best_pred = max(predictions, key=lambda x: x.confidence if x.success else 0)
            if best_pred.success:
                return TradingDecision(
                    symbol=symbol,
                    final_action=best_pred.action,
                    ensemble_confidence=best_pred.confidence * 0.5,  # Reduced confidence
                    consensus_level=0.0,
                    models_used=1,
                    response_time=time.time() - start_time,
                    individual_predictions=[{
                        'model_name': best_pred.model_name,
                        'action': best_pred.action,
                        'confidence': best_pred.confidence,
                        'reasoning': best_pred.reasoning
                    }],
                    voting_strategies={},
                    market_context={},
                    risk_assessment={'overall_risk': 0.8, 'risk_level': 'HIGH', 'recommendation': 'Emergency fallback decision'},
                    timestamp=datetime.now(),
                    cache_hit=False,
                    meta_voting=False
                )

        # Ultimate fallback
        return TradingDecision(
            symbol=symbol,
            final_action="HOLD",
            ensemble_confidence=0.1,
            consensus_level=0.0,
            models_used=0,
            response_time=time.time() - start_time,
            individual_predictions=[],
            voting_strategies={},
            market_context={},
            risk_assessment={'overall_risk': 1.0, 'risk_level': 'CRITICAL', 'recommendation': 'No models available - HOLD recommended'},
            timestamp=datetime.now(),
            cache_hit=False,
            meta_voting=False
        )

    # Utility methods for monitoring and optimization
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        total_decisions = len(self.decision_history)
        cache_hits = len([d for d in self.decision_history if d.cache_hit])

        avg_response_time = sum(d.response_time for d in self.decision_history) / max(1, total_decisions)
        avg_confidence = sum(d.ensemble_confidence for d in self.decision_history) / max(1, total_decisions)

        return {
            'timestamp': datetime.now().isoformat(),
            'total_decisions': total_decisions,
            'cache_hit_rate': cache_hits / max(1, total_decisions),
            'avg_response_time': avg_response_time,
            'avg_confidence': avg_confidence,
            'model_count': len(self.models),
            'cache_size': len(self.prediction_cache),
            'model_performance': {
                name: {
                    'success_rate': metrics['success_rate'],
                    'avg_response_time': metrics['avg_response_time'],
                    'performance_score': metrics['performance_score'],
                    'tier': metrics['tier']
                }
                for name, metrics in self.model_performance.items()
            }
        }

    def get_top_performers(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing models"""
        performers = []
        for name, metrics in self.model_performance.items():
            if metrics['total_calls'] > 0:
                performers.append({
                    'model_name': name,
                    'performance_score': metrics['performance_score'],
                    'success_rate': metrics['success_rate'],
                    'avg_response_time': metrics['avg_response_time'],
                    'tier': metrics['tier'],
                    'total_calls': metrics['total_calls']
                })

        return sorted(performers, key=lambda x: x['performance_score'], reverse=True)[:limit]

    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        logger.info("🏥 Running comprehensive health check...")

        # Test top 3 models quickly
        top_models = self.get_top_performers(3)
        health_results = {}

        test_prompt = "Quick test. ACTION: HOLD, CONFIDENCE: 0.5, REASONING: Health check"

        for model_info in top_models:
            model_name = model_info['model_name']
            try:
                start_time = time.time()
                result = self._call_model_ultra_fast(model_name, test_prompt, 10, self.models[model_name])
                test_time = time.time() - start_time

                health_results[model_name] = {
                    'status': 'healthy' if result.success else 'unhealthy',
                    'response_time': test_time,
                    'tier': model_info['tier'],
                    'error': result.error_message if not result.success else None
                }
            except Exception as e:
                health_results[model_name] = {
                    'status': 'error',
                    'error': str(e),
                    'tier': model_info.get('tier', 'unknown')
                }

        # Overall health assessment
        healthy_count = sum(1 for r in health_results.values() if r['status'] == 'healthy')
        total_tested = len(health_results)

        if healthy_count == 0:
            overall_status = 'critical'
        elif healthy_count < total_tested // 2:
            overall_status = 'degraded'
        else:
            overall_status = 'healthy'

        return {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'healthy_models': healthy_count,
            'total_tested': total_tested,
            'model_health': health_results,
            'system_metrics': {
                'cache_size': len(self.prediction_cache),
                'decision_history': len(self.decision_history),
                'total_models': len(self.models)
            }
        }

# Testing and demonstration functions
async def test_ultimate_system():
    """Comprehensive test of the ultimate system"""
    print("🚀 ULTIMATE ENSEMBLE SYSTEM - COMPREHENSIVE TEST")
    print("=" * 80)

    # Initialize system
    print("🔧 Initializing Ultimate System...")
    system = UltimateEnsembleSystem()

    # Health check
    print("\n🏥 System Health Check...")
    health = await system.health_check()
    print(f"   Overall Status: {health['overall_status']}")
    print(f"   Healthy Models: {health['healthy_models']}/{health['total_tested']}")

    # Test different urgency levels
    test_cases = [
        {"symbol": "AAPL", "urgency": "emergency", "context": {"trend": "bearish", "volatility": "high"}},
        {"symbol": "TSLA", "urgency": "fast", "context": {"trend": "bullish", "volatility": "medium"}},
        {"symbol": "BTC", "urgency": "normal", "context": {"trend": "volatile", "volatility": "extreme"}},
        {"symbol": "NVDA", "urgency": "comprehensive", "context": {"trend": "bullish", "volatility": "low"}}
    ]

    print(f"\n🧪 TESTING DIFFERENT URGENCY LEVELS")
    print("-" * 80)

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📈 TEST {i}: {test_case['symbol']} ({test_case['urgency'].upper()})")
        print("-" * 50)

        start_time = time.time()
        try:
            decision = await system.get_ultimate_trading_decision(
                test_case['symbol'],
                test_case['context'],
                test_case['urgency']
            )

            print(f"   ✅ Action: {decision.final_action}")
            print(f"   ✅ Confidence: {decision.ensemble_confidence:.2f}")
            print(f"   ✅ Consensus: {decision.consensus_level:.2f}")
            print(f"   ✅ Models Used: {decision.models_used}")
            print(f"   ✅ Response Time: {decision.response_time:.1f}s")
            print(f"   ✅ Risk Level: {decision.risk_assessment['risk_level']}")
            print(f"   ✅ Cached: {decision.cache_hit}")

        except Exception as e:
            print(f"   ❌ Error: {e}")

    # Test caching
    print(f"\n🔄 CACHE PERFORMANCE TEST")
    print("-" * 50)

    # Repeat first test to check caching
    test_case = test_cases[0]
    start_time = time.time()
    cached_decision = await system.get_ultimate_trading_decision(
        test_case['symbol'],
        test_case['context'],
        test_case['urgency']
    )
    cache_time = time.time() - start_time

    print(f"   Cache Hit: {cached_decision.cache_hit}")
    print(f"   Cache Response Time: {cache_time:.3f}s")

    # Performance report
    print(f"\n📊 PERFORMANCE REPORT")
    print("-" * 50)

    report = system.get_performance_report()
    print(f"   Total Decisions: {report['total_decisions']}")
    print(f"   Cache Hit Rate: {report['cache_hit_rate']:.1%}")
    print(f"   Avg Response Time: {report['avg_response_time']:.1f}s")
    print(f"   Avg Confidence: {report['avg_confidence']:.2f}")

    # Top performers
    print(f"\n🏆 TOP PERFORMING MODELS")
    print("-" * 50)

    top_performers = system.get_top_performers(5)
    for i, model in enumerate(top_performers, 1):
        print(f"   {i}. {model['model_name'][:40]}...")
        print(f"      Performance Score: {model['performance_score']:.3f}")
        print(f"      Success Rate: {model['success_rate']:.2f}")
        print(f"      Avg Time: {model['avg_response_time']:.1f}s")
        print(f"      Tier: {model['tier']}")

    print(f"\n✅ Ultimate System test completed!")
    print(f"\n🎉 SYSTEM CAPABILITIES DEMONSTRATED:")
    print(f"   ⚡ Lightning-fast emergency decisions")
    print(f"   🚀 Multi-tier model optimization")
    print(f"   🧠 Advanced meta-voting algorithms")
    print(f"   🛡️ Comprehensive risk assessment")
    print(f"   📊 Real-time performance tracking")
    print(f"   🔄 Intelligent caching system")

if __name__ == "__main__":
    asyncio.run(test_ultimate_system())
