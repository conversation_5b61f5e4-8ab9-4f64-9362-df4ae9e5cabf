#!/usr/bin/env python3
"""
Quick test of the Ultimate Ensemble System
"""

import asyncio
import time
from ultimate_ensemble_system import UltimateEnsembleSystem

async def quick_test():
    """Quick test to verify everything works"""
    print("🚀 QUICK ULTIMATE SYSTEM TEST")
    print("=" * 50)
    
    try:
        # Initialize system
        print("🔧 Initializing system...")
        system = UltimateEnsembleSystem()
        print(f"✅ System initialized with {len(system.models)} models")
        
        # Quick health check
        print("\n🏥 Health check...")
        health = await system.health_check()
        print(f"✅ Status: {health['overall_status']}")
        print(f"✅ Healthy models: {health['healthy_models']}/{health['total_tested']}")
        
        # Quick decision test
        print(f"\n🎯 Testing emergency decision...")
        start_time = time.time()
        
        decision = await system.get_ultimate_trading_decision(
            "AAPL", 
            {"trend": "bullish", "volatility": "medium"}, 
            "emergency"
        )
        
        test_time = time.time() - start_time
        
        print(f"✅ Decision: {decision.final_action}")
        print(f"✅ Confidence: {decision.ensemble_confidence:.2f}")
        print(f"✅ Models used: {decision.models_used}")
        print(f"✅ Time: {test_time:.1f}s")
        print(f"✅ Risk level: {decision.risk_assessment['risk_level']}")
        
        # Test caching
        print(f"\n🔄 Testing cache...")
        start_time = time.time()
        
        cached_decision = await system.get_ultimate_trading_decision(
            "AAPL", 
            {"trend": "bullish", "volatility": "medium"}, 
            "emergency"
        )
        
        cache_time = time.time() - start_time
        print(f"✅ Cache hit: {cached_decision.cache_hit}")
        print(f"✅ Cache time: {cache_time:.3f}s")
        
        # Performance report
        print(f"\n📊 Performance report...")
        report = system.get_performance_report()
        print(f"✅ Total decisions: {report['total_decisions']}")
        print(f"✅ Cache hit rate: {report['cache_hit_rate']:.1%}")
        print(f"✅ Avg response time: {report['avg_response_time']:.1f}s")
        
        print(f"\n🎉 ULTIMATE SYSTEM IS WORKING!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(quick_test())
    if success:
        print("\n✅ ALL SYSTEMS GO - READY FOR EVERYTHING!")
    else:
        print("\n❌ System needs attention")
