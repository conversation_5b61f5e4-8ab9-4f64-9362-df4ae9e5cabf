#!/usr/bin/env python3
"""
Demo Script for Local Ensemble Voting System
Shows the system working with real local models
"""

import asyncio
import time
from local_ensemble_voting_system import LocalEnsembleVotingSystem

async def demo_ensemble_system():
    """Demonstrate the local ensemble voting system"""
    print("🚀 LOCAL ENSEMBLE VOTING SYSTEM DEMO")
    print("=" * 60)
    print("✅ Using ONLY local Ollama models - NO API calls!")
    print("✅ All models are running locally on your machine")
    print("✅ No external dependencies or costs")
    print()
    
    # Initialize the ensemble system
    print("🔧 Initializing Local Ensemble System...")
    ensemble = LocalEnsembleVotingSystem()
    print(f"   ✅ Loaded {len(ensemble.models)} specialized AI models")
    print()
    
    # List available models
    print("🤖 Available AI Models:")
    for model_name, config in ensemble.models.items():
        print(f"   • {model_name}")
        print(f"     Specialization: {config['specialization']}")
        print(f"     Weight: {config['weight']}")
        print()
    
    # Demo trading decisions
    test_cases = [
        {
            "symbol": "AAPL",
            "context": {
                "market_trend": "bullish",
                "volatility": "medium",
                "volume": "high",
                "news_sentiment": "positive",
                "sector": "technology"
            }
        },
        {
            "symbol": "BTC",
            "context": {
                "market_trend": "volatile",
                "volatility": "high",
                "volume": "very_high",
                "news_sentiment": "mixed",
                "sector": "cryptocurrency"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        symbol = test_case["symbol"]
        context = test_case["context"]
        
        print(f"📈 TEST {i}: Ensemble Analysis for {symbol}")
        print("-" * 40)
        
        # Show market context
        print("📊 Market Context:")
        for key, value in context.items():
            print(f"   {key}: {value}")
        print()
        
        # Get ensemble decision
        print(f"🧠 Querying {len(ensemble.models)} AI models...")
        start_time = time.time()
        
        try:
            decision = await ensemble.get_ensemble_prediction(symbol, context)
            total_time = time.time() - start_time
            
            # Display results
            print(f"\n🎯 ENSEMBLE DECISION:")
            print(f"   Symbol: {symbol}")
            print(f"   Action: {decision.get('final_action', 'UNKNOWN')}")
            print(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
            print(f"   Consensus Level: {decision.get('consensus_level', 0.0):.2f}")
            print(f"   Models Responded: {decision.get('models_used', 0)}")
            print(f"   Response Time: {total_time:.1f}s")
            
            # Show individual model predictions
            if 'individual_predictions' in decision:
                print(f"\n📋 Individual Model Predictions:")
                for pred in decision['individual_predictions']:
                    print(f"   • {pred['model_name'][:30]}...")
                    print(f"     Action: {pred['action']}")
                    print(f"     Confidence: {pred['confidence']:.2f}")
                    print(f"     Specialization: {pred['specialization']}")
                    print(f"     Response Time: {pred['response_time']:.1f}s")
                    print()
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("=" * 60)
        print()
    
    print("✅ Demo completed successfully!")
    print("🎉 Local Ensemble Voting System is fully operational!")
    print()
    print("📝 Key Features Demonstrated:")
    print("   ✅ Multiple specialized AI models working together")
    print("   ✅ Weighted ensemble voting with confidence scoring")
    print("   ✅ Parallel model execution for speed")
    print("   ✅ Robust error handling and fallback mechanisms")
    print("   ✅ 100% local execution - no external API calls")
    print("   ✅ Real-time trading decision making")

if __name__ == "__main__":
    asyncio.run(demo_ensemble_system())
