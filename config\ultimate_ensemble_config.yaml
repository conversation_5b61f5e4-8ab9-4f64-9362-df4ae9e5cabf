# ULTIMATE ENSEMBLE TRADING SYSTEM CONFIGURATION
# Optimized for maximum performance, reliability, and speed

ensemble:
  enabled: true
  
  # PERFORMANCE OPTIMIZATION SETTINGS
  max_parallel_models: 16  # Aggressive parallelization
  timeout_per_model: 25    # Reduced for speed
  cache_enabled: true
  cache_ttl: 180          # 3 minutes for faster updates
  adaptive_timeouts: true
  performance_tracking: true
  
  # DECISION THRESHOLDS (Optimized for more decisions)
  confidence_threshold: 0.6   # Lower for more trading opportunities
  consensus_threshold: 0.5    # Lower for faster consensus
  
  # ADVANCED FEATURES
  risk_integration: true
  market_regime_detection: true
  adaptive_learning: true
  meta_voting: true
  
  # URGENCY-BASED MODEL SELECTION
  urgency_levels:
    emergency:
      max_models: 3
      max_timeout: 15
      tiers: ["lightning"]
      
    fast:
      max_models: 6
      max_timeout: 25
      tiers: ["lightning", "fast"]
      
    normal:
      max_models: 8
      max_timeout: 35
      tiers: ["lightning", "fast", "powerful"]
      
    comprehensive:
      max_models: 12
      max_timeout: 50
      tiers: ["lightning", "fast", "powerful"]

  # TIER 1: LIGHTNING FAST MODELS (< 20s)
  lightning_models:
  - name: unrestricted-noryon-deepscaler-finance-v2-latest:latest
    specialization: scalability_analysis
    weight: 0.20
    confidence_threshold: 0.60
    priority: 1
    timeout: 15
    tier: lightning
    enabled: true
    
  - name: unrestricted-noryon-falcon3-finance-v1-latest:latest
    specialization: momentum_analysis
    weight: 0.18
    confidence_threshold: 0.60
    priority: 2
    timeout: 18
    tier: lightning
    enabled: true
    
  - name: unrestricted-noryon-cogito-finance-v2-latest:latest
    specialization: pattern_recognition
    weight: 0.16
    confidence_threshold: 0.60
    priority: 3
    timeout: 20
    tier: lightning
    enabled: true

  # TIER 2: FAST MODELS (20-35s)
  fast_models:
  - name: marco-o1:latest
    specialization: reasoning_analysis
    weight: 0.15
    confidence_threshold: 0.70
    priority: 4
    timeout: 25
    tier: fast
    enabled: true
    
  - name: unrestricted-noryon-phi-4-9b-finance-latest:latest
    specialization: technical_analysis
    weight: 0.14
    confidence_threshold: 0.65
    priority: 5
    timeout: 30
    tier: fast
    enabled: true
    
  - name: unrestricted-noryon-exaone-deep-finance-v2-latest:latest
    specialization: deep_analysis
    weight: 0.12
    confidence_threshold: 0.65
    priority: 6
    timeout: 35
    tier: fast
    enabled: true

  # TIER 3: POWERFUL MODELS (35-50s)
  powerful_models:
  - name: unrestricted-noryon-deepseek-r1-finance-v2-latest:latest
    specialization: risk_assessment
    weight: 0.10
    confidence_threshold: 0.70
    priority: 7
    timeout: 40
    tier: powerful
    enabled: true
    selective_use: true
    
  - name: unrestricted-noryon-qwen3-finance-v2-latest:latest
    specialization: market_analysis
    weight: 0.08
    confidence_threshold: 0.65
    priority: 8
    timeout: 45
    tier: powerful
    enabled: true
    selective_use: true
    
  - name: unrestricted-noryon-gemma-3-12b-finance-latest:latest
    specialization: fundamental_analysis
    weight: 0.07
    confidence_threshold: 0.65
    priority: 9
    timeout: 50
    tier: powerful
    enabled: true
    selective_use: true

# VOTING STRATEGIES CONFIGURATION
voting_strategies:
  tier_weighted:
    enabled: true
    weight: 0.30
    description: "Weighted by model tier and performance"
    tier_multipliers:
      lightning: 1.2
      fast: 1.0
      powerful: 0.8
      
  confidence_squared:
    enabled: true
    weight: 0.25
    description: "Squared confidence weighting for emphasis"
    
  performance_adaptive:
    enabled: true
    weight: 0.25
    description: "Adapted to historical model performance"
    
  risk_adjusted:
    enabled: true
    weight: 0.20
    description: "Risk-adjusted based on market conditions"

# RISK ASSESSMENT CONFIGURATION
risk_assessment:
  enabled: true
  
  # Risk factor weights
  consensus_weight: 0.25
  confidence_weight: 0.25
  disagreement_weight: 0.20
  volatility_weight: 0.15
  timing_weight: 0.15
  
  # Risk thresholds
  low_risk_threshold: 0.3
  medium_risk_threshold: 0.6
  high_risk_threshold: 0.8
  
  # Market volatility risk mapping
  volatility_risk:
    low: 0.2
    medium: 0.5
    high: 0.8
    extreme: 1.0

# PERFORMANCE OPTIMIZATION
performance:
  tracking_enabled: true
  adaptive_learning: true
  
  # Performance metrics weights
  success_rate_weight: 0.4
  speed_weight: 0.3
  confidence_weight: 0.3
  
  # Optimization settings
  min_calls_for_optimization: 5
  performance_update_frequency: 10  # Every 10 calls
  
# CACHING CONFIGURATION
cache:
  enabled: true
  ttl_seconds: 180  # 3 minutes
  max_entries: 200
  cleanup_frequency: 50  # Clean every 50 entries
  
# LOGGING CONFIGURATION
logging:
  level: INFO
  file: ultimate_ensemble.log
  max_size_mb: 100
  backup_count: 5
  
# MONITORING AND HEALTH CHECK
monitoring:
  health_check_enabled: true
  health_check_timeout: 10
  performance_reporting: true
  
# MARKET CONTEXT DEFAULTS
market_context:
  default_volatility: medium
  default_trend: neutral
  
# EMERGENCY FALLBACK SETTINGS
emergency:
  fallback_action: HOLD
  fallback_confidence: 0.1
  max_emergency_timeout: 5
