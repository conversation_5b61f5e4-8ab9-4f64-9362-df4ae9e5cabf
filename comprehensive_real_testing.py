#!/usr/bin/env python3
"""
COMPREHENSIVE REAL TESTING SUITE
20+ tests for everything - ACTUAL tests, NO fake stuff, NO simulated AI
Real model responses, real performance measurements, real verification
"""

import asyncio
import time
import json
import statistics
from datetime import datetime
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict
import logging

# Import all systems for testing
from ultimate_ensemble_system import UltimateEnsembleSystem
from optimized_ensemble_system import OptimizedEnsembleVotingSystem
from local_ensemble_voting_system import LocalEnsembleVotingSystem

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RealTestResult:
    """Real test result - no fake data allowed"""
    test_name: str
    passed: bool
    execution_time: float
    real_model_responses: int
    actual_ai_calls: int
    error_message: str = ""
    test_data: Dict[str, Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class ComprehensiveRealTestSuite:
    """Comprehensive testing suite with REAL tests only"""
    
    def __init__(self):
        self.systems = {
            'ultimate': UltimateEnsembleSystem(),
            'optimized': OptimizedEnsembleVotingSystem(),
            'local': LocalEnsembleVotingSystem()
        }
        
        self.test_results = []
        self.total_ai_calls = 0
        self.total_model_responses = 0
        
        logger.info("🧪 Comprehensive Real Testing Suite initialized")
        logger.info(f"   Systems to test: {len(self.systems)}")
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all 20+ comprehensive tests"""
        logger.info("🚀 Starting comprehensive real testing suite...")
        
        test_methods = [
            # Core System Tests
            self._test_system_initialization,
            self._test_model_availability,
            self._test_individual_model_calls,
            self._test_ensemble_voting,
            self._test_emergency_mode,
            self._test_fast_mode,
            self._test_normal_mode,
            self._test_comprehensive_mode,
            
            # Performance Tests
            self._test_response_times,
            self._test_parallel_execution,
            self._test_cache_functionality,
            self._test_memory_usage,
            self._test_error_handling,
            self._test_timeout_handling,
            
            # Decision Quality Tests
            self._test_decision_consistency,
            self._test_confidence_scoring,
            self._test_consensus_calculation,
            self._test_risk_assessment,
            self._test_voting_strategies,
            self._test_meta_voting,
            
            # Stress Tests
            self._test_high_load,
            self._test_rapid_requests,
            self._test_concurrent_users,
            self._test_system_recovery,
            
            # Integration Tests
            self._test_cross_system_compatibility
        ]
        
        for test_method in test_methods:
            try:
                result = await test_method()
                self.test_results.append(result)
                
                status = "✅ PASSED" if result.passed else "❌ FAILED"
                logger.info(f"{status} {result.test_name} ({result.execution_time:.2f}s)")
                
            except Exception as e:
                failed_result = RealTestResult(
                    test_name=test_method.__name__,
                    passed=False,
                    execution_time=0.0,
                    real_model_responses=0,
                    actual_ai_calls=0,
                    error_message=str(e)
                )
                self.test_results.append(failed_result)
                logger.error(f"❌ FAILED {test_method.__name__}: {e}")
        
        return self._generate_test_report()
    
    async def _test_system_initialization(self) -> RealTestResult:
        """Test 1: System initialization"""
        start_time = time.time()
        
        try:
            # Test all systems can initialize
            for system_name, system in self.systems.items():
                assert system is not None, f"{system_name} system is None"
                assert hasattr(system, 'models'), f"{system_name} has no models"
                assert len(system.models) > 0, f"{system_name} has no models loaded"
            
            return RealTestResult(
                test_name="System Initialization",
                passed=True,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                test_data={
                    'systems_tested': len(self.systems),
                    'total_models': sum(len(s.models) for s in self.systems.values() if hasattr(s, 'models'))
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="System Initialization",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_model_availability(self) -> RealTestResult:
        """Test 2: Model availability check"""
        start_time = time.time()
        
        try:
            available_models = 0
            total_models = 0
            
            for system_name, system in self.systems.items():
                if hasattr(system, 'models'):
                    for model_name in system.models.keys():
                        total_models += 1
                        # Check if model is configured and enabled
                        model_config = system.models[model_name]
                        if model_config.get('enabled', True):
                            available_models += 1
            
            success_rate = available_models / total_models if total_models > 0 else 0
            
            return RealTestResult(
                test_name="Model Availability",
                passed=success_rate > 0.8,  # 80% models should be available
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                test_data={
                    'available_models': available_models,
                    'total_models': total_models,
                    'availability_rate': success_rate
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Model Availability",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_individual_model_calls(self) -> RealTestResult:
        """Test 3: Individual model calls with REAL AI responses"""
        start_time = time.time()
        
        try:
            successful_calls = 0
            total_calls = 0
            actual_responses = 0
            
            # Test ultimate system's individual model calls
            system = self.systems['ultimate']
            test_prompt = "Quick test. ACTION: HOLD, CONFIDENCE: 0.5, REASONING: Test call"
            
            # Test top 3 fastest models
            lightning_models = [name for name, config in system.models.items() 
                              if config.get('tier') == 'lightning'][:3]
            
            for model_name in lightning_models:
                total_calls += 1
                try:
                    # Make actual AI model call
                    result = system._call_model_ultra_fast(
                        model_name, 
                        test_prompt, 
                        15,  # 15 second timeout
                        system.models[model_name]
                    )
                    
                    if result.success and len(result.response) > 10:
                        successful_calls += 1
                        actual_responses += 1
                        self.total_ai_calls += 1
                        
                except Exception as e:
                    logger.warning(f"Model {model_name} failed: {e}")
            
            success_rate = successful_calls / total_calls if total_calls > 0 else 0
            
            return RealTestResult(
                test_name="Individual Model Calls",
                passed=success_rate > 0.5,  # 50% success rate minimum
                execution_time=time.time() - start_time,
                real_model_responses=actual_responses,
                actual_ai_calls=total_calls,
                test_data={
                    'successful_calls': successful_calls,
                    'total_calls': total_calls,
                    'success_rate': success_rate,
                    'models_tested': lightning_models
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Individual Model Calls",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_ensemble_voting(self) -> RealTestResult:
        """Test 4: Ensemble voting with REAL AI decisions"""
        start_time = time.time()
        
        try:
            # Test ensemble decision making
            system = self.systems['ultimate']
            
            decision = await system.get_ultimate_trading_decision(
                "AAPL", 
                {"trend": "bullish", "volatility": "medium"}, 
                "emergency"
            )
            
            # Verify real ensemble voting occurred
            has_final_action = hasattr(decision, 'final_action') and decision.final_action in ['BUY', 'SELL', 'HOLD']
            has_confidence = hasattr(decision, 'ensemble_confidence') and 0 <= decision.ensemble_confidence <= 1
            has_models_used = hasattr(decision, 'models_used') and decision.models_used > 0
            has_voting_strategies = hasattr(decision, 'voting_strategies') and len(decision.voting_strategies) > 0
            
            self.total_ai_calls += decision.models_used
            self.total_model_responses += decision.models_used
            
            return RealTestResult(
                test_name="Ensemble Voting",
                passed=has_final_action and has_confidence and has_models_used,
                execution_time=time.time() - start_time,
                real_model_responses=decision.models_used,
                actual_ai_calls=decision.models_used,
                test_data={
                    'final_action': decision.final_action,
                    'confidence': decision.ensemble_confidence,
                    'models_used': decision.models_used,
                    'voting_strategies': len(decision.voting_strategies) if hasattr(decision, 'voting_strategies') else 0,
                    'consensus_level': getattr(decision, 'consensus_level', 0)
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Ensemble Voting",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_emergency_mode(self) -> RealTestResult:
        """Test 5: Emergency mode speed and functionality"""
        start_time = time.time()
        
        try:
            system = self.systems['ultimate']
            
            # Test emergency mode (should be < 20s)
            decision = await system.get_ultimate_trading_decision(
                "TSLA", 
                {"trend": "bearish", "volatility": "high"}, 
                "emergency"
            )
            
            response_time = time.time() - start_time
            
            # Emergency mode requirements
            is_fast_enough = response_time < 25  # Allow 25s buffer
            has_decision = hasattr(decision, 'final_action')
            used_models = getattr(decision, 'models_used', 0)
            
            self.total_ai_calls += used_models
            self.total_model_responses += used_models
            
            return RealTestResult(
                test_name="Emergency Mode",
                passed=is_fast_enough and has_decision and used_models > 0,
                execution_time=response_time,
                real_model_responses=used_models,
                actual_ai_calls=used_models,
                test_data={
                    'response_time': response_time,
                    'target_time': 20,
                    'models_used': used_models,
                    'final_action': getattr(decision, 'final_action', 'UNKNOWN')
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Emergency Mode",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_response_times(self) -> RealTestResult:
        """Test 9: Response time consistency"""
        start_time = time.time()
        
        try:
            system = self.systems['ultimate']
            response_times = []
            total_responses = 0
            
            # Test multiple symbols for consistency
            test_symbols = ["AAPL", "MSFT", "GOOGL"]
            
            for symbol in test_symbols:
                test_start = time.time()
                decision = await system.get_ultimate_trading_decision(
                    symbol, 
                    {"trend": "neutral"}, 
                    "emergency"
                )
                test_time = time.time() - test_start
                response_times.append(test_time)
                total_responses += getattr(decision, 'models_used', 0)
            
            avg_time = statistics.mean(response_times)
            std_dev = statistics.stdev(response_times) if len(response_times) > 1 else 0
            
            # Consistency check - standard deviation should be reasonable
            is_consistent = std_dev < avg_time * 0.5  # Within 50% of average
            
            self.total_ai_calls += total_responses
            self.total_model_responses += total_responses
            
            return RealTestResult(
                test_name="Response Time Consistency",
                passed=is_consistent and avg_time < 30,
                execution_time=time.time() - start_time,
                real_model_responses=total_responses,
                actual_ai_calls=total_responses,
                test_data={
                    'response_times': response_times,
                    'avg_time': avg_time,
                    'std_deviation': std_dev,
                    'consistency_ratio': std_dev / avg_time if avg_time > 0 else 0
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Response Time Consistency",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    async def _test_cache_functionality(self) -> RealTestResult:
        """Test 11: Cache functionality with REAL measurements"""
        start_time = time.time()
        
        try:
            system = self.systems['ultimate']
            
            # First call (should miss cache)
            first_start = time.time()
            decision1 = await system.get_ultimate_trading_decision(
                "BTC", 
                {"trend": "bullish"}, 
                "emergency"
            )
            first_time = time.time() - first_start
            
            # Second call (should hit cache)
            second_start = time.time()
            decision2 = await system.get_ultimate_trading_decision(
                "BTC", 
                {"trend": "bullish"}, 
                "emergency"
            )
            second_time = time.time() - second_start
            
            # Check if cache worked
            cache_hit = getattr(decision2, 'cache_hit', False)
            speedup = (first_time - second_time) / first_time if first_time > 0 else 0
            
            models_used = getattr(decision1, 'models_used', 0)
            self.total_ai_calls += models_used
            self.total_model_responses += models_used
            
            return RealTestResult(
                test_name="Cache Functionality",
                passed=cache_hit or speedup > 0.5,  # Either cache hit or significant speedup
                execution_time=time.time() - start_time,
                real_model_responses=models_used,
                actual_ai_calls=models_used,
                test_data={
                    'first_call_time': first_time,
                    'second_call_time': second_time,
                    'cache_hit': cache_hit,
                    'speedup_ratio': speedup
                }
            )
            
        except Exception as e:
            return RealTestResult(
                test_name="Cache Functionality",
                passed=False,
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                error_message=str(e)
            )
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed_tests = sum(1 for result in self.test_results if result.passed)
        total_tests = len(self.test_results)
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        total_execution_time = sum(result.execution_time for result in self.test_results)
        avg_execution_time = total_execution_time / total_tests if total_tests > 0 else 0
        
        return {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': success_rate,
                'total_execution_time': total_execution_time,
                'avg_execution_time': avg_execution_time,
                'total_ai_calls': self.total_ai_calls,
                'total_model_responses': self.total_model_responses
            },
            'test_results': [asdict(result) for result in self.test_results],
            'failed_tests': [
                {
                    'test_name': result.test_name,
                    'error': result.error_message,
                    'execution_time': result.execution_time
                }
                for result in self.test_results if not result.passed
            ]
        }

    # Additional test methods to complete 20+ tests
    async def _test_fast_mode(self) -> RealTestResult:
        """Test 6: Fast mode functionality"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("NVDA", {"trend": "bullish"}, "fast")

            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Fast Mode",
                passed=response_time < 35 and models_used > 0,
                execution_time=response_time,
                real_model_responses=models_used,
                actual_ai_calls=models_used,
                test_data={'response_time': response_time, 'models_used': models_used}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Fast Mode", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_normal_mode(self) -> RealTestResult:
        """Test 7: Normal mode functionality"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("MSFT", {"trend": "neutral"}, "normal")

            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Normal Mode",
                passed=response_time < 50 and models_used > 0,
                execution_time=response_time,
                real_model_responses=models_used,
                actual_ai_calls=models_used,
                test_data={'response_time': response_time, 'models_used': models_used}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Normal Mode", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_comprehensive_mode(self) -> RealTestResult:
        """Test 8: Comprehensive mode functionality"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("GOOGL", {"trend": "bullish"}, "comprehensive")

            response_time = time.time() - start_time
            models_used = getattr(decision, 'models_used', 0)

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Comprehensive Mode",
                passed=response_time < 70 and models_used > 2,
                execution_time=response_time,
                real_model_responses=models_used,
                actual_ai_calls=models_used,
                test_data={'response_time': response_time, 'models_used': models_used}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Comprehensive Mode", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_parallel_execution(self) -> RealTestResult:
        """Test 10: Parallel execution efficiency"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']

            # Test parallel execution of multiple symbols
            symbols = ["AAPL", "TSLA", "BTC"]
            tasks = [
                system.get_ultimate_trading_decision(symbol, {"trend": "neutral"}, "emergency")
                for symbol in symbols
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)
            successful_results = [r for r in results if not isinstance(r, Exception)]

            total_models = sum(getattr(r, 'models_used', 0) for r in successful_results)
            self.total_ai_calls += total_models
            self.total_model_responses += total_models

            return RealTestResult(
                test_name="Parallel Execution",
                passed=len(successful_results) >= 2,
                execution_time=time.time() - start_time,
                real_model_responses=total_models,
                actual_ai_calls=total_models,
                test_data={'successful_results': len(successful_results), 'total_symbols': len(symbols)}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Parallel Execution", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_memory_usage(self) -> RealTestResult:
        """Test 12: Memory usage monitoring"""
        start_time = time.time()
        try:
            import psutil
            process = psutil.Process()

            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Make several decisions to test memory usage
            system = self.systems['ultimate']
            for i in range(5):
                decision = await system.get_ultimate_trading_decision(f"TEST{i}", {"trend": "neutral"}, "emergency")

            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory

            return RealTestResult(
                test_name="Memory Usage",
                passed=memory_increase < 100,  # Less than 100MB increase
                execution_time=time.time() - start_time,
                real_model_responses=0,
                actual_ai_calls=0,
                test_data={'initial_memory': initial_memory, 'final_memory': final_memory, 'increase': memory_increase}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Memory Usage", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_error_handling(self) -> RealTestResult:
        """Test 13: Error handling robustness"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']

            # Test with invalid inputs
            try:
                decision = await system.get_ultimate_trading_decision("", {}, "invalid_urgency")
                # Should handle gracefully
            except:
                pass  # Expected to fail

            # Test with valid inputs after error
            decision = await system.get_ultimate_trading_decision("AAPL", {"trend": "neutral"}, "emergency")
            models_used = getattr(decision, 'models_used', 0)

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Error Handling",
                passed=models_used > 0,  # System recovered and worked
                execution_time=time.time() - start_time,
                real_model_responses=models_used,
                actual_ai_calls=models_used,
                test_data={'recovery_successful': models_used > 0}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Error Handling", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_timeout_handling(self) -> RealTestResult:
        """Test 14: Timeout handling"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']

            # Test with very short timeout (should handle gracefully)
            original_timeout = system.timeout_base
            system.timeout_base = 1  # Very short timeout

            decision = await system.get_ultimate_trading_decision("AAPL", {"trend": "neutral"}, "emergency")

            # Restore original timeout
            system.timeout_base = original_timeout

            # System should still provide some response
            has_action = hasattr(decision, 'final_action')

            return RealTestResult(
                test_name="Timeout Handling",
                passed=has_action,
                execution_time=time.time() - start_time,
                real_model_responses=getattr(decision, 'models_used', 0),
                actual_ai_calls=getattr(decision, 'models_used', 0),
                test_data={'has_action': has_action, 'action': getattr(decision, 'final_action', 'NONE')}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Timeout Handling", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    # Additional critical tests (15-25)
    async def _test_decision_consistency(self) -> RealTestResult:
        """Test 15: Decision consistency"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decisions = []
            total_models = 0

            for i in range(3):
                decision = await system.get_ultimate_trading_decision("AAPL", {"trend": "bullish"}, "emergency")
                decisions.append(decision)
                total_models += getattr(decision, 'models_used', 0)

            actions = [getattr(d, 'final_action', 'UNKNOWN') for d in decisions]
            consistent = len(set(actions)) <= 2

            self.total_ai_calls += total_models
            self.total_model_responses += total_models

            return RealTestResult(
                test_name="Decision Consistency", passed=consistent and len(decisions) == 3,
                execution_time=time.time() - start_time, real_model_responses=total_models,
                actual_ai_calls=total_models, test_data={'actions': actions, 'consistent': consistent}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Decision Consistency", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_confidence_scoring(self) -> RealTestResult:
        """Test 16: Confidence scoring"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("TSLA", {"trend": "bullish"}, "normal")

            confidence = getattr(decision, 'ensemble_confidence', 0)
            models_used = getattr(decision, 'models_used', 0)
            valid_confidence = 0 <= confidence <= 1

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Confidence Scoring", passed=valid_confidence and models_used > 0,
                execution_time=time.time() - start_time, real_model_responses=models_used,
                actual_ai_calls=models_used, test_data={'confidence': confidence, 'valid_range': valid_confidence}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Confidence Scoring", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_consensus_calculation(self) -> RealTestResult:
        """Test 17: Consensus calculation"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("BTC", {"trend": "volatile"}, "comprehensive")

            consensus = getattr(decision, 'consensus_level', 0)
            models_used = getattr(decision, 'models_used', 0)
            valid_consensus = 0 <= consensus <= 1

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Consensus Calculation", passed=valid_consensus and models_used > 1,
                execution_time=time.time() - start_time, real_model_responses=models_used,
                actual_ai_calls=models_used, test_data={'consensus': consensus, 'models_used': models_used}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Consensus Calculation", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_risk_assessment(self) -> RealTestResult:
        """Test 18: Risk assessment"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("NVDA", {"trend": "bullish", "volatility": "high"}, "normal")

            risk_assessment = getattr(decision, 'risk_assessment', {})
            models_used = getattr(decision, 'models_used', 0)
            has_risk_level = 'risk_level' in risk_assessment
            has_overall_risk = 'overall_risk' in risk_assessment

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Risk Assessment", passed=has_risk_level and has_overall_risk and models_used > 0,
                execution_time=time.time() - start_time, real_model_responses=models_used,
                actual_ai_calls=models_used, test_data={'risk_assessment': risk_assessment}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Risk Assessment", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_voting_strategies(self) -> RealTestResult:
        """Test 19: Voting strategies"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("MSFT", {"trend": "bullish"}, "normal")

            voting_strategies = getattr(decision, 'voting_strategies', {})
            models_used = getattr(decision, 'models_used', 0)
            has_multiple_strategies = len(voting_strategies) >= 2

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Voting Strategies", passed=has_multiple_strategies and models_used > 0,
                execution_time=time.time() - start_time, real_model_responses=models_used,
                actual_ai_calls=models_used, test_data={'strategies_count': len(voting_strategies)}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Voting Strategies", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

    async def _test_meta_voting(self) -> RealTestResult:
        """Test 20: Meta-voting"""
        start_time = time.time()
        try:
            system = self.systems['ultimate']
            decision = await system.get_ultimate_trading_decision("GOOGL", {"trend": "neutral"}, "comprehensive")

            meta_voting = getattr(decision, 'meta_voting', False)
            models_used = getattr(decision, 'models_used', 0)

            self.total_ai_calls += models_used
            self.total_model_responses += models_used

            return RealTestResult(
                test_name="Meta Voting", passed=meta_voting and models_used > 1,
                execution_time=time.time() - start_time, real_model_responses=models_used,
                actual_ai_calls=models_used, test_data={'meta_voting_enabled': meta_voting}
            )
        except Exception as e:
            return RealTestResult(
                test_name="Meta Voting", passed=False, execution_time=time.time() - start_time,
                real_model_responses=0, actual_ai_calls=0, error_message=str(e)
            )

async def main():
    """Run comprehensive real testing"""
    print("🧪 COMPREHENSIVE REAL TESTING SUITE")
    print("=" * 80)
    print("🔥 REAL TESTS ONLY - NO FAKE STUFF, NO SIMULATIONS")
    print()
    
    test_suite = ComprehensiveRealTestSuite()
    
    # Run all tests
    report = await test_suite.run_all_tests()
    
    # Display results
    summary = report['summary']
    print(f"\n📊 TEST RESULTS SUMMARY:")
    print(f"   Total Tests: {summary['total_tests']}")
    print(f"   Passed: {summary['passed_tests']} ✅")
    print(f"   Failed: {summary['failed_tests']} ❌")
    print(f"   Success Rate: {summary['success_rate']:.1%}")
    print(f"   Total Execution Time: {summary['total_execution_time']:.1f}s")
    print(f"   Real AI Calls Made: {summary['total_ai_calls']}")
    print(f"   Real Model Responses: {summary['total_model_responses']}")
    
    if report['failed_tests']:
        print(f"\n❌ FAILED TESTS:")
        for failed in report['failed_tests']:
            print(f"   - {failed['test_name']}: {failed['error']}")
    
    # Save detailed report
    with open('comprehensive_test_report.json', 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n💾 Detailed report saved to: comprehensive_test_report.json")
    print(f"\n✅ Comprehensive testing completed!")
    
    return summary['success_rate'] > 0.8  # 80% success rate required

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🎉 SYSTEM PASSES COMPREHENSIVE TESTING!")
    else:
        print("\n⚠️ SYSTEM NEEDS ATTENTION - CHECK FAILED TESTS")
