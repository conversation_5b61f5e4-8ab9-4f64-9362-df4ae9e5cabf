{"timestamp": "2025-06-08T20:48:44.754554", "summary": {"total_tests": 22, "passed_tests": 16, "failed_tests": 6, "success_rate": 0.7272727272727273, "total_execution_time": 809.7777967453003, "avg_execution_time": 36.808081670240924, "total_ai_calls": 83, "total_responses": 80}, "test_results": [{"test_name": "System Initialization", "passed": true, "execution_time": 8.106231689453125e-06, "real_ai_calls": 0, "details": {"ultimate_models": 9, "local_models": 15}, "error": ""}, {"test_name": "Emergency Mode", "passed": true, "execution_time": 13.28449821472168, "real_ai_calls": 3, "details": {"action": "BUY", "confidence": 0.6901930947128507, "models_used": 3, "response_time": 13.28449821472168}, "error": ""}, {"test_name": "Fast Mode", "passed": false, "execution_time": 31.283119440078735, "real_ai_calls": 0, "details": {}, "error": "1 (of 6) futures unfinished"}, {"test_name": "Normal Mode", "passed": false, "execution_time": 45.03296732902527, "real_ai_calls": 0, "details": {}, "error": "1 (of 8) futures unfinished"}, {"test_name": "Individual Models", "passed": true, "execution_time": 25.505630254745483, "real_ai_calls": 3, "details": {"successful_calls": 3, "total_calls": 3, "success_rate": 1.0, "models_tested": ["unrestricted-noryon-deepscaler-finance-v2-latest:latest", "unrestricted-noryon-falcon3-finance-v1-latest:latest", "unrestricted-noryon-cogito-finance-v2-latest:latest"]}, "error": ""}, {"test_name": "Ensemble Voting", "passed": true, "execution_time": 9.79850149154663, "real_ai_calls": 3, "details": {"models_used": 3, "has_action": true, "has_confidence": true, "has_voting": true, "action": "BUY"}, "error": ""}, {"test_name": "<PERSON><PERSON>", "passed": true, "execution_time": 8.46442437171936, "real_ai_calls": 3, "details": {"first_time": 8.464195489883423, "second_time": 0.00022649765014648438, "cache_hit": true, "speedup": 0.9999732404987081}, "error": ""}, {"test_name": "Response Times", "passed": true, "execution_time": 29.052632808685303, "real_ai_calls": 7, "details": {"avg_time": 9.684208313624064, "times": [10.007380247116089, 10.022222518920898, 9.023022174835205]}, "error": ""}, {"test_name": "Confidence Scoring", "passed": false, "execution_time": 45.03957200050354, "real_ai_calls": 0, "details": {}, "error": "1 (of 8) futures unfinished"}, {"test_name": "Consensus Calculation", "passed": false, "execution_time": 50.03244423866272, "real_ai_calls": 0, "details": {}, "error": "2 (of 9) futures unfinished"}, {"test_name": "Risk Assessment", "passed": false, "execution_time": 40.03116726875305, "real_ai_calls": 0, "details": {}, "error": "1 (of 8) futures unfinished"}, {"test_name": "Voting Strategies", "passed": true, "execution_time": 39.97151565551758, "real_ai_calls": 4, "details": {"strategies_count": 4, "strategies": ["tier_weighted", "confidence_squared", "performance_adaptive", "risk_adjusted"]}, "error": ""}, {"test_name": "Meta Voting", "passed": true, "execution_time": 44.381293296813965, "real_ai_calls": 3, "details": {"meta_voting_enabled": true, "models_used": 3}, "error": ""}, {"test_name": "Parallel Execution", "passed": true, "execution_time": 40.73322510719299, "real_ai_calls": 5, "details": {"successful_results": 2, "total_symbols": 3}, "error": ""}, {"test_name": "Erro<PERSON>", "passed": false, "execution_time": 70.04375624656677, "real_ai_calls": 0, "details": {}, "error": "1 (of 3) futures unfinished"}, {"test_name": "Timeout Handling", "passed": true, "execution_time": 10.262902975082397, "real_ai_calls": 0, "details": {"has_action": true, "action": "HOLD"}, "error": ""}, {"test_name": "Memory Usage", "passed": true, "execution_time": 25.712758541107178, "real_ai_calls": 0, "details": {"initial_memory": 53.03125, "final_memory": 52.23828125, "increase": -0.79296875}, "error": ""}, {"test_name": "Decision Consistency", "passed": true, "execution_time": 8.528035163879395, "real_ai_calls": 9, "details": {"actions": ["BUY", "BUY", "BUY"], "consistent": true}, "error": ""}, {"test_name": "High Load", "passed": true, "execution_time": 15.775160551071167, "real_ai_calls": 24, "details": {"total_requests": 10, "successful": 10, "success_rate": 1.0}, "error": ""}, {"test_name": "Rapid Requests", "passed": true, "execution_time": 47.81217575073242, "real_ai_calls": 14, "details": {"successful_requests": 5, "total_requests": 5}, "error": ""}, {"test_name": "System Recovery", "passed": true, "execution_time": 50.0364465713501, "real_ai_calls": 3, "details": {"recovery_successful": true}, "error": ""}, {"test_name": "Cross-System Compatibility", "passed": true, "execution_time": 158.99556136131287, "real_ai_calls": 2, "details": {"successful_systems": 1, "total_systems": 2}, "error": ""}], "failed_tests": [{"test_name": "Fast Mode", "error": "1 (of 6) futures unfinished"}, {"test_name": "Normal Mode", "error": "1 (of 8) futures unfinished"}, {"test_name": "Confidence Scoring", "error": "1 (of 8) futures unfinished"}, {"test_name": "Consensus Calculation", "error": "2 (of 9) futures unfinished"}, {"test_name": "Risk Assessment", "error": "1 (of 8) futures unfinished"}, {"test_name": "Erro<PERSON>", "error": "1 (of 3) futures unfinished"}]}