#!/usr/bin/env python3
"""
QUICK SYSTEM FIX - MAKING ALL MODES WORK
Based on test results: Emergency mode works, but Fast/Normal/Comprehensive modes timeout
"""

import asyncio
import time
from ultimate_ensemble_system import UltimateEnsembleSystem

async def apply_quick_fixes():
    """Apply quick fixes to make all modes work"""
    print("🔧 APPLYING QUICK FIXES TO MAKE ALL MODES WORK")
    print("=" * 60)
    
    # Initialize system
    system = UltimateEnsembleSystem()
    
    print(f"📊 Current System Status:")
    print(f"   Models: {len(system.models)}")
    print(f"   Base timeout: {system.timeout_base}s")
    print(f"   Max workers: {system.max_workers}")
    
    # Fix 1: Reduce timeouts aggressively
    print(f"\n🔧 Fix 1: Reducing timeouts...")
    system.timeout_base = 15  # Reduced from 25s
    
    for model_name, config in system.models.items():
        if config.get('tier') == 'lightning':
            config['timeout'] = 12  # Max 12s for lightning
        elif config.get('tier') == 'fast':
            config['timeout'] = 18  # Max 18s for fast  
        elif config.get('tier') == 'powerful':
            config['timeout'] = 25  # Max 25s for powerful
    
    print(f"   ✅ Base timeout: {system.timeout_base}s")
    print(f"   ✅ Lightning models: 12s max")
    print(f"   ✅ Fast models: 18s max")
    print(f"   ✅ Powerful models: 25s max")
    
    # Fix 2: Limit models per urgency level
    print(f"\n🔧 Fix 2: Limiting models per urgency...")
    
    # Override model selection for each urgency
    system.urgency_model_limits = {
        'emergency': 2,      # Only 2 fastest models
        'fast': 3,          # Only 3 models
        'normal': 4,        # Only 4 models  
        'comprehensive': 5   # Only 5 models
    }
    
    print(f"   ✅ Emergency: 2 models max")
    print(f"   ✅ Fast: 3 models max")
    print(f"   ✅ Normal: 4 models max")
    print(f"   ✅ Comprehensive: 5 models max")
    
    # Fix 3: Test each mode
    print(f"\n🧪 Testing fixed modes...")
    
    # Test Emergency Mode
    print(f"\n⚡ Testing Emergency Mode...")
    start_time = time.time()
    try:
        decision = await system.get_ultimate_trading_decision("AAPL", {"trend": "bullish"}, "emergency")
        emergency_time = time.time() - start_time
        emergency_success = emergency_time < 20 and hasattr(decision, 'final_action')
        
        print(f"   ✅ Emergency: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {emergency_time:.1f}s")
    except Exception as e:
        print(f"   ❌ Emergency failed: {e}")
        emergency_success = False
    
    # Test Fast Mode
    print(f"\n🚀 Testing Fast Mode...")
    start_time = time.time()
    try:
        decision = await system.get_ultimate_trading_decision("TSLA", {"trend": "bearish"}, "fast")
        fast_time = time.time() - start_time
        fast_success = fast_time < 30 and hasattr(decision, 'final_action')
        
        print(f"   ✅ Fast: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {fast_time:.1f}s")
    except Exception as e:
        print(f"   ❌ Fast failed: {e}")
        fast_success = False
    
    # Test Normal Mode  
    print(f"\n📊 Testing Normal Mode...")
    start_time = time.time()
    try:
        decision = await system.get_ultimate_trading_decision("BTC", {"trend": "volatile"}, "normal")
        normal_time = time.time() - start_time
        normal_success = normal_time < 40 and hasattr(decision, 'final_action')
        
        print(f"   ✅ Normal: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {normal_time:.1f}s")
    except Exception as e:
        print(f"   ❌ Normal failed: {e}")
        normal_success = False
    
    # Test Comprehensive Mode
    print(f"\n💪 Testing Comprehensive Mode...")
    start_time = time.time()
    try:
        decision = await system.get_ultimate_trading_decision("NVDA", {"trend": "bullish"}, "comprehensive")
        comp_time = time.time() - start_time
        comp_success = comp_time < 50 and hasattr(decision, 'final_action')
        
        print(f"   ✅ Comprehensive: {decision.final_action} ({decision.ensemble_confidence:.2f}) - {comp_time:.1f}s")
    except Exception as e:
        print(f"   ❌ Comprehensive failed: {e}")
        comp_success = False
    
    # Summary
    total_modes = 4
    working_modes = sum([emergency_success, fast_success, normal_success, comp_success])
    
    print(f"\n📊 FIX RESULTS:")
    print(f"   Working Modes: {working_modes}/{total_modes}")
    print(f"   Success Rate: {working_modes/total_modes:.1%}")
    
    if working_modes == total_modes:
        print(f"\n🎉 ALL MODES NOW WORKING PERFECTLY!")
        print(f"🔥 SYSTEM IS FULLY BULLETPROOF!")
    elif working_modes >= 3:
        print(f"\n✅ MAJOR IMPROVEMENT - MOST MODES WORKING!")
    else:
        print(f"\n⚠️ STILL NEEDS MORE FIXES")
    
    return working_modes == total_modes

async def main():
    """Run quick fixes"""
    success = await apply_quick_fixes()
    
    if success:
        print(f"\n🚀 SYSTEM FIXED AND READY!")
    else:
        print(f"\n🔧 ADDITIONAL FIXES NEEDED")

if __name__ == "__main__":
    asyncio.run(main())
