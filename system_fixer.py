#!/usr/bin/env python3
"""
SYSTEM FIXER - MAKING ALL MODES BULLETPROOF
Fixing timeout issues, model reliability, and performance problems
"""

import asyncio
import time
import json
from datetime import datetime
from typing import Dict, List, Any
import logging

# Import systems to fix
from ultimate_ensemble_system import UltimateEnsembleSystem
from local_ensemble_voting_system import LocalEnsembleVotingSystem

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemFixer:
    """Fix all system issues and make it bulletproof"""
    
    def __init__(self):
        self.ultimate_system = UltimateEnsembleSystem()
        self.local_system = LocalEnsembleVotingSystem()
        self.fixes_applied = []
        
        logger.info("🔧 System Fixer initialized")
    
    async def fix_all_issues(self) -> Dict[str, Any]:
        """Apply all fixes to make system bulletproof"""
        logger.info("🚀 Starting comprehensive system fixes...")
        
        fixes = [
            self.fix_timeout_issues,
            self.fix_model_reliability,
            self.fix_error_handling,
            self.optimize_performance,
            self.add_fallback_mechanisms,
            self.improve_caching,
            self.enhance_parallel_processing
        ]
        
        for fix in fixes:
            try:
                result = await fix()
                self.fixes_applied.append(result)
                logger.info(f"✅ {result['fix_name']} - {result['status']}")
            except Exception as e:
                logger.error(f"❌ Fix failed: {e}")
        
        return await self.verify_fixes()
    
    async def fix_timeout_issues(self) -> Dict[str, Any]:
        """Fix 1: Resolve timeout issues"""
        logger.info("🔧 Fixing timeout issues...")
        
        # Reduce timeouts for problematic models
        for system in [self.ultimate_system]:
            if hasattr(system, 'models'):
                for model_name, config in system.models.items():
                    # Aggressive timeout reduction
                    if config.get('tier') == 'lightning':
                        config['timeout'] = 15  # Max 15s for lightning
                    elif config.get('tier') == 'fast':
                        config['timeout'] = 25  # Max 25s for fast
                    elif config.get('tier') == 'powerful':
                        config['timeout'] = 35  # Max 35s for powerful
                    
                    # Mark slow models as backup only
                    if config.get('timeout', 60) > 40:
                        config['backup_only'] = True
                        config['weight'] = 0.1  # Lower priority
        
        # Adjust system-wide timeouts
        if hasattr(self.ultimate_system, 'timeout_base'):
            self.ultimate_system.timeout_base = 20  # Reduced from 25
        
        return {
            'fix_name': 'Timeout Issues',
            'status': 'Applied aggressive timeout limits',
            'details': {
                'lightning_timeout': 15,
                'fast_timeout': 25,
                'powerful_timeout': 35,
                'base_timeout': 20
            }
        }
    
    async def fix_model_reliability(self) -> Dict[str, Any]:
        """Fix 2: Improve model reliability"""
        logger.info("🔧 Fixing model reliability...")
        
        # Test each model and mark unreliable ones
        reliable_models = []
        unreliable_models = []
        
        for system in [self.ultimate_system]:
            if hasattr(system, 'models'):
                for model_name, config in system.models.items():
                    try:
                        # Quick test call
                        start_time = time.time()
                        result = system._call_model_ultra_fast(
                            model_name,
                            "Quick test. ACTION: HOLD, CONFIDENCE: 0.5",
                            10,  # 10 second test timeout
                            config
                        )
                        response_time = time.time() - start_time
                        
                        if result.success and response_time < 15:
                            reliable_models.append(model_name)
                            config['reliable'] = True
                            config['avg_response_time'] = response_time
                        else:
                            unreliable_models.append(model_name)
                            config['reliable'] = False
                            config['backup_only'] = True
                            
                    except Exception as e:
                        unreliable_models.append(model_name)
                        config['reliable'] = False
                        config['backup_only'] = True
                        logger.warning(f"Model {model_name} failed test: {e}")
        
        return {
            'fix_name': 'Model Reliability',
            'status': f'Tested all models - {len(reliable_models)} reliable, {len(unreliable_models)} unreliable',
            'details': {
                'reliable_models': reliable_models,
                'unreliable_models': unreliable_models
            }
        }
    
    async def fix_error_handling(self) -> Dict[str, Any]:
        """Fix 3: Improve error handling"""
        logger.info("🔧 Fixing error handling...")
        
        # Add better error recovery to ultimate system
        if hasattr(self.ultimate_system, 'models'):
            # Create backup model lists for each tier
            lightning_backups = [name for name, config in self.ultimate_system.models.items() 
                               if config.get('tier') == 'lightning' and config.get('reliable', True)]
            fast_backups = [name for name, config in self.ultimate_system.models.items() 
                          if config.get('tier') == 'fast' and config.get('reliable', True)]
            
            # Store backup lists
            self.ultimate_system.lightning_backups = lightning_backups[:2]  # Top 2 lightning
            self.ultimate_system.fast_backups = fast_backups[:2]  # Top 2 fast
        
        return {
            'fix_name': 'Error Handling',
            'status': 'Added backup model lists and recovery mechanisms',
            'details': {
                'lightning_backups': getattr(self.ultimate_system, 'lightning_backups', []),
                'fast_backups': getattr(self.ultimate_system, 'fast_backups', [])
            }
        }
    
    async def optimize_performance(self) -> Dict[str, Any]:
        """Fix 4: Optimize performance"""
        logger.info("🔧 Optimizing performance...")
        
        # Increase parallel workers for reliable models only
        for system in [self.ultimate_system]:
            if hasattr(system, 'max_workers'):
                system.max_workers = min(32, len([m for m, c in system.models.items() 
                                                if c.get('reliable', True)]) * 4)
        
        # Optimize model selection for each urgency level
        urgency_configs = {
            'emergency': {
                'max_models': 2,  # Only 2 fastest models
                'timeout_multiplier': 0.8,
                'require_reliable': True
            },
            'fast': {
                'max_models': 3,  # Only 3 models
                'timeout_multiplier': 0.9,
                'require_reliable': True
            },
            'normal': {
                'max_models': 4,  # Only 4 models
                'timeout_multiplier': 1.0,
                'require_reliable': True
            },
            'comprehensive': {
                'max_models': 5,  # Only 5 models
                'timeout_multiplier': 1.1,
                'require_reliable': False  # Can use some unreliable models
            }
        }
        
        # Apply urgency configs
        self.ultimate_system.urgency_configs = urgency_configs
        
        return {
            'fix_name': 'Performance Optimization',
            'status': 'Applied model limits and timeout optimizations',
            'details': urgency_configs
        }
    
    async def add_fallback_mechanisms(self) -> Dict[str, Any]:
        """Fix 5: Add fallback mechanisms"""
        logger.info("🔧 Adding fallback mechanisms...")
        
        # Create fallback decision logic
        fallback_decisions = {
            'high_volatility': {'action': 'HOLD', 'confidence': 0.3, 'reasoning': 'High volatility fallback'},
            'bullish_trend': {'action': 'BUY', 'confidence': 0.4, 'reasoning': 'Bullish trend fallback'},
            'bearish_trend': {'action': 'SELL', 'confidence': 0.4, 'reasoning': 'Bearish trend fallback'},
            'neutral': {'action': 'HOLD', 'confidence': 0.3, 'reasoning': 'Neutral fallback'},
            'default': {'action': 'HOLD', 'confidence': 0.2, 'reasoning': 'Default safety fallback'}
        }
        
        self.ultimate_system.fallback_decisions = fallback_decisions
        
        return {
            'fix_name': 'Fallback Mechanisms',
            'status': 'Added intelligent fallback decisions',
            'details': {
                'fallback_scenarios': len(fallback_decisions),
                'default_action': 'HOLD'
            }
        }
    
    async def improve_caching(self) -> Dict[str, Any]:
        """Fix 6: Improve caching system"""
        logger.info("🔧 Improving caching...")
        
        # Optimize cache settings
        for system in [self.ultimate_system]:
            if hasattr(system, 'cache_ttl'):
                system.cache_ttl = 300  # 5 minutes cache
            if hasattr(system, 'prediction_cache'):
                # Limit cache size to prevent memory issues
                if len(system.prediction_cache) > 100:
                    # Keep only most recent 50 entries
                    cache_items = list(system.prediction_cache.items())
                    system.prediction_cache = dict(cache_items[-50:])
        
        return {
            'fix_name': 'Caching Improvements',
            'status': 'Optimized cache TTL and size limits',
            'details': {
                'cache_ttl': 300,
                'max_cache_size': 100
            }
        }
    
    async def enhance_parallel_processing(self) -> Dict[str, Any]:
        """Fix 7: Enhance parallel processing"""
        logger.info("🔧 Enhancing parallel processing...")
        
        # Optimize parallel execution settings
        import multiprocessing as mp
        optimal_workers = min(mp.cpu_count() * 2, 16)  # Conservative but effective
        
        for system in [self.ultimate_system]:
            if hasattr(system, 'max_workers'):
                system.max_workers = optimal_workers
        
        return {
            'fix_name': 'Parallel Processing',
            'status': f'Set optimal worker count to {optimal_workers}',
            'details': {
                'max_workers': optimal_workers,
                'cpu_count': mp.cpu_count()
            }
        }
    
    async def verify_fixes(self) -> Dict[str, Any]:
        """Verify all fixes are working"""
        logger.info("🧪 Verifying fixes...")
        
        verification_results = []
        
        # Test emergency mode
        try:
            start_time = time.time()
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "AAPL", {"trend": "bullish"}, "emergency"
            )
            emergency_time = time.time() - start_time
            emergency_success = emergency_time < 20 and hasattr(decision, 'final_action')
            
            verification_results.append({
                'test': 'Emergency Mode',
                'success': emergency_success,
                'time': emergency_time,
                'models_used': getattr(decision, 'models_used', 0)
            })
        except Exception as e:
            verification_results.append({
                'test': 'Emergency Mode',
                'success': False,
                'error': str(e)
            })
        
        # Test fast mode
        try:
            start_time = time.time()
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "TSLA", {"trend": "bearish"}, "fast"
            )
            fast_time = time.time() - start_time
            fast_success = fast_time < 35 and hasattr(decision, 'final_action')
            
            verification_results.append({
                'test': 'Fast Mode',
                'success': fast_success,
                'time': fast_time,
                'models_used': getattr(decision, 'models_used', 0)
            })
        except Exception as e:
            verification_results.append({
                'test': 'Fast Mode',
                'success': False,
                'error': str(e)
            })
        
        # Test normal mode
        try:
            start_time = time.time()
            decision = await self.ultimate_system.get_ultimate_trading_decision(
                "BTC", {"trend": "volatile"}, "normal"
            )
            normal_time = time.time() - start_time
            normal_success = normal_time < 50 and hasattr(decision, 'final_action')
            
            verification_results.append({
                'test': 'Normal Mode',
                'success': normal_success,
                'time': normal_time,
                'models_used': getattr(decision, 'models_used', 0)
            })
        except Exception as e:
            verification_results.append({
                'test': 'Normal Mode',
                'success': False,
                'error': str(e)
            })
        
        successful_tests = sum(1 for r in verification_results if r.get('success', False))
        total_tests = len(verification_results)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': len(self.fixes_applied),
            'verification_results': verification_results,
            'success_rate': successful_tests / total_tests if total_tests > 0 else 0,
            'summary': {
                'total_fixes': len(self.fixes_applied),
                'successful_tests': successful_tests,
                'total_tests': total_tests,
                'all_modes_working': successful_tests == total_tests
            }
        }

async def main():
    """Run system fixes"""
    print("🔧 SYSTEM FIXER - MAKING IT BULLETPROOF")
    print("=" * 60)
    print("🎯 Fixing timeout issues, model reliability, and performance")
    print()
    
    fixer = SystemFixer()
    results = await fixer.fix_all_issues()
    
    print(f"\n📊 FIX RESULTS:")
    print(f"   Fixes Applied: {results['summary']['total_fixes']}")
    print(f"   Tests Passed: {results['summary']['successful_tests']}/{results['summary']['total_tests']}")
    print(f"   Success Rate: {results['summary']['success_rate']:.1%}")
    print(f"   All Modes Working: {'✅ YES' if results['summary']['all_modes_working'] else '❌ NO'}")
    
    print(f"\n🧪 VERIFICATION RESULTS:")
    for result in results['verification_results']:
        status = "✅ PASSED" if result.get('success', False) else "❌ FAILED"
        time_info = f" ({result.get('time', 0):.1f}s)" if 'time' in result else ""
        models_info = f" - {result.get('models_used', 0)} models" if 'models_used' in result else ""
        error_info = f" - {result.get('error', '')}" if 'error' in result else ""
        print(f"   {status} {result['test']}{time_info}{models_info}{error_info}")
    
    # Save results
    with open('system_fix_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: system_fix_results.json")
    
    if results['summary']['all_modes_working']:
        print(f"\n🎉 ALL MODES NOW WORKING PERFECTLY!")
    else:
        print(f"\n⚠️ Some modes still need attention")
    
    return results['summary']['all_modes_working']

if __name__ == "__main__":
    success = asyncio.run(main())
    if success:
        print("\n🔥 SYSTEM IS NOW BULLETPROOF!")
    else:
        print("\n🔧 Additional fixes may be needed")
