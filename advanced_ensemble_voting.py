#!/usr/bin/env python3
"""
Enhanced Local Ensemble Voting System
Combines multiple local AI models for superior trading decisions
Uses only Ollama local models - NO API CALLS
"""

import asyncio
import concurrent.futures
import time
import json
import yaml
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import defaultdict, Counter
import logging

# Import enhanced local components
from optimized_model_caller import EnhancedLocalModelCaller, ModelResponse, ModelStatus

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VotingMethod(Enum):
    MAJORITY = "majority"
    WEIGHTED = "weighted"
    CONFIDENCE_WEIGHTED = "confidence_weighted"
    PERFORMANCE_WEIGHTED = "performance_weighted"
    ADAPTIVE = "adaptive"

@dataclass
class ModelPrediction:
    model_name: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]
    reasoning: str
    response_time: float
    timestamp: datetime

@dataclass
class EnsembleDecision:
    final_action: str
    ensemble_confidence: float
    individual_predictions: List[ModelPrediction]
    voting_method: VotingMethod
    consensus_level: float
    conflicting_signals: bool
    execution_time: float
    timestamp: datetime

@dataclass
class ModelPerformance:
    model_name: str
    total_predictions: int
    correct_predictions: int
    accuracy: float
    avg_confidence: float
    avg_response_time: float
    profit_contribution: float
    recent_performance: float  # Last 10 trades
    weight: float

class LocalEnsembleVotingSystem:
    """Enhanced Local Ensemble Voting System - Uses only local Ollama models"""

    def __init__(self, config_path: str = "config/ensemble_config.yaml"):
        self.model_caller = EnhancedLocalModelCaller()
        self.config_path = config_path
        self.config = self._load_config()

        # Load models from configuration
        self.models = self._load_models_from_config()
        self.model_performance = {}
        self.voting_history = []

        logger.info(f"Initialized Local Ensemble with {len(self.models)} models")

    def _load_config(self) -> Dict[str, Any]:
        """Load ensemble configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file loading fails"""
        return {
            'ensemble': {
                'enabled': True,
                'confidence_threshold': 0.7,
                'voting_strategy': 'adaptive',
                'models': []
            }
        }

    def _load_models_from_config(self) -> Dict[str, Dict[str, Any]]:
        """Load model configurations"""
        models = {}

        for model_config in self.config.get('ensemble', {}).get('models', []):
            name = model_config.get('name')
            if name:
                models[name] = {
                    'specialization': model_config.get('specialization', 'general'),
                    'weight': model_config.get('weight', 0.1),
                    'confidence_threshold': model_config.get('confidence_threshold', 0.6),
                    'priority': model_config.get('priority', 5),
                    'type': model_config.get('type', 'ollama')
                }

        # Fallback models if config is empty
        if not models:
            models = self._get_fallback_models()

        return models

    def _get_fallback_models(self) -> Dict[str, Dict[str, Any]]:
        """Get fallback model configuration"""
        return {
            "marco-o1:latest": {
                'specialization': 'reasoning_analysis',
                'weight': 0.20,
                'confidence_threshold': 0.75,
                'priority': 1,
                'type': 'ollama'
            },
            "unrestricted-noryon-deepseek-r1-finance-v2-latest:latest": {
                'specialization': 'risk_assessment',
                'weight': 0.18,
                'confidence_threshold': 0.70,
                'priority': 2,
                'type': 'ollama'
            },
            "unrestricted-noryon-phi-4-9b-finance-latest:latest": {
                'specialization': 'technical_analysis',
                'weight': 0.17,
                'confidence_threshold': 0.70,
                'priority': 3,
                'type': 'ollama'
            },
            "unrestricted-noryon-qwen3-finance-v2-latest:latest": {
                'specialization': 'market_analysis',
                'weight': 0.16,
                'confidence_threshold': 0.65,
                'priority': 4,
                'type': 'ollama'
            },
            "unrestricted-noryon-gemma-3-12b-finance-latest:latest": {
                'specialization': 'fundamental_analysis',
                'weight': 0.15,
                'confidence_threshold': 0.65,
                'priority': 5,
                'type': 'ollama'
            }
        }

    async def get_ensemble_prediction(self, symbol: str, market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get ensemble prediction from multiple local models"""
        start_time = time.time()

        logger.info(f"🧠 Local Ensemble Analysis: {symbol}")
        logger.info(f"   Models available: {len(self.models)}")

        # Generate prompt with market context
        prompt = self._create_enhanced_prompt(symbol, market_context)

        # Get predictions from all models in parallel
        predictions = await self._get_parallel_predictions(prompt, symbol)

        # Filter successful predictions
        valid_predictions = [p for p in predictions if p['success']]

        if len(valid_predictions) < 2:
            logger.warning(f"Insufficient models responded ({len(valid_predictions)})")
            return self._create_fallback_decision(symbol, valid_predictions, start_time)

        # Apply ensemble voting
        decision = self._apply_ensemble_voting(valid_predictions, start_time)

        logger.info(f"📊 ENSEMBLE RESULT:")
        logger.info(f"   Action: {decision.get('final_action', 'HOLD')}")
        logger.info(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
        logger.info(f"   Models used: {len(valid_predictions)}")

        return decision

    def _create_enhanced_prompt(self, symbol: str, market_context: Dict[str, Any] = None) -> str:
        """Create enhanced prompt for trading analysis"""
        base_prompt = f"""
Analyze {symbol} for trading decision. Provide:

1. ACTION: BUY/SELL/HOLD
2. CONFIDENCE: 0.0-1.0
3. REASONING: Brief explanation

Market Context:
"""

        if market_context:
            for key, value in market_context.items():
                base_prompt += f"- {key}: {value}\n"

        base_prompt += """
Respond in format:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [explanation]
"""

        return base_prompt

    async def _get_parallel_predictions(self, prompt: str, symbol: str) -> List[Dict[str, Any]]:
        """Get predictions from all models in parallel"""
        predictions = []

        # Use ThreadPoolExecutor for parallel model calls
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            future_to_model = {}

            for model_name, model_config in self.models.items():
                future = executor.submit(self._call_single_model, model_name, prompt)
                future_to_model[future] = model_name

            for future in concurrent.futures.as_completed(future_to_model, timeout=90):
                model_name = future_to_model[future]
                try:
                    result = future.result()
                    predictions.append({
                        'model_name': model_name,
                        'success': result.success,
                        'response': result.response,
                        'response_time': result.response_time,
                        'specialization': self.models[model_name]['specialization'],
                        'weight': self.models[model_name]['weight']
                    })
                except Exception as e:
                    logger.error(f"Model {model_name} failed: {e}")
                    predictions.append({
                        'model_name': model_name,
                        'success': False,
                        'response': '',
                        'response_time': 0.0,
                        'error': str(e),
                        'specialization': self.models[model_name]['specialization'],
                        'weight': self.models[model_name]['weight']
                    })

        return predictions

    def _call_single_model(self, model_name: str, prompt: str) -> ModelResponse:
        """Call a single model with error handling"""
        try:
            return self.model_caller.call_model_with_retries(model_name, prompt, max_retries=1)
        except Exception as e:
            logger.error(f"Error calling {model_name}: {e}")
            return ModelResponse(
                success=False,
                response="",
                response_time=0.0,
                model_name=model_name,
                status=ModelStatus.ERROR,
                error_message=str(e)
            )

    def _apply_ensemble_voting(self, predictions: List[Dict[str, Any]], start_time: float) -> Dict[str, Any]:
        """Apply ensemble voting to predictions"""
        if not predictions:
            return self._create_empty_decision(start_time)

        # Parse predictions
        parsed_predictions = []
        for pred in predictions:
            parsed = self._parse_prediction(pred)
            if parsed:
                parsed_predictions.append(parsed)

        if not parsed_predictions:
            return self._create_empty_decision(start_time)

        # Apply weighted voting
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        for pred in parsed_predictions:
            weight = pred['weight']
            action_votes[pred['action']] += weight * pred['confidence']
            confidence_sum += pred['confidence'] * weight
            total_weight += weight

        # Determine final action
        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'final_action': final_action,
            'ensemble_confidence': ensemble_confidence,
            'individual_predictions': parsed_predictions,
            'consensus_level': len([p for p in parsed_predictions if p['action'] == final_action]) / len(parsed_predictions),
            'response_time': time.time() - start_time,
            'models_used': len(parsed_predictions)
        }

    def _parse_prediction(self, prediction: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse individual model prediction"""
        try:
            response = prediction['response'].upper()

            # Extract action
            action = 'HOLD'
            if 'BUY' in response:
                action = 'BUY'
            elif 'SELL' in response:
                action = 'SELL'

            # Extract confidence
            confidence = 0.5
            import re
            conf_match = re.search(r'CONFIDENCE:\s*([0-9.]+)', response)
            if conf_match:
                confidence = float(conf_match.group(1))

            return {
                'model_name': prediction['model_name'],
                'action': action,
                'confidence': confidence,
                'weight': prediction['weight'],
                'specialization': prediction['specialization'],
                'response_time': prediction['response_time']
            }
        except Exception as e:
            logger.error(f"Error parsing prediction: {e}")
            return None

    def _create_empty_decision(self, start_time: float) -> Dict[str, Any]:
        """Create empty decision when no valid predictions"""
        return {
            'final_action': 'HOLD',
            'ensemble_confidence': 0.0,
            'individual_predictions': [],
            'consensus_level': 0.0,
            'response_time': time.time() - start_time,
            'models_used': 0,
            'error': 'No valid predictions available'
        }

    def _create_fallback_decision(self, symbol: str, predictions: List[Dict[str, Any]], start_time: float) -> Dict[str, Any]:
        """Create fallback decision when insufficient models respond"""
        if predictions:
            # Use the best available prediction
            best_pred = max(predictions, key=lambda x: x.get('confidence', 0.0))
            return {
                'final_action': best_pred.get('action', 'HOLD'),
                'ensemble_confidence': best_pred.get('confidence', 0.0) * 0.5,  # Reduced confidence
                'individual_predictions': predictions,
                'consensus_level': 0.0,
                'response_time': time.time() - start_time,
                'models_used': len(predictions),
                'fallback': True
            }
        else:
            return self._create_empty_decision(start_time)
    
    # End of LocalEnsembleVotingSystem class

# Test function for the local ensemble system
async def test_local_ensemble():
    """Test the local ensemble voting system"""
    print("🚀 LOCAL ENSEMBLE VOTING SYSTEM TEST")
    print("=" * 60)

    # Initialize ensemble
    ensemble = LocalEnsembleVotingSystem()

    # Test symbols
    test_symbols = ["BTC", "ETH"]

    for symbol in test_symbols:
        print(f"\n🧪 Testing ensemble prediction for {symbol}...")

        # Market context
        market_context = {
            "market_trend": "bullish",
            "volatility": "medium",
            "volume": "high",
            "news_sentiment": "positive"
        }

        # Get ensemble decision
        decision = await ensemble.get_ensemble_prediction(symbol, market_context)

        print(f"\n📊 ENSEMBLE RESULT:")
        print(f"   Action: {decision.get('final_action', 'UNKNOWN')}")
        print(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
        print(f"   Consensus: {decision.get('consensus_level', 0.0):.2f}")
        print(f"   Models used: {decision.get('models_used', 0)}")

    print(f"\n✅ Local Ensemble Voting System test completed!")

if __name__ == "__main__":
    asyncio.run(test_local_ensemble())
        """Performance-weighted voting based on historical accuracy"""
        action_weights = defaultdict(float)
        total_weight = 0
        
        for pred in predictions:
            # Find model performance
            model_perf = None
            for perf in self.model_performance.values():
                if perf.model_name == pred.model_name:
                    model_perf = perf
                    break
            
            if model_perf:
                weight = model_perf.recent_performance * pred.confidence
                action_weights[pred.action] += weight
                total_weight += weight
        
        if total_weight == 0:
            return self._majority_voting(predictions, start_time)
        
        # Normalize weights
        for action in action_weights:
            action_weights[action] /= total_weight
        
        final_action = max(action_weights.items(), key=lambda x: x[1])[0]
        ensemble_confidence = action_weights[final_action]
        consensus_level = action_weights[final_action]
        
        return EnsembleDecision(
            final_action=final_action,
            ensemble_confidence=ensemble_confidence,
            individual_predictions=predictions,
            voting_method=VotingMethod.PERFORMANCE_WEIGHTED,
            consensus_level=consensus_level,
            conflicting_signals=len(action_weights) > 1,
            execution_time=time.time() - start_time,
            timestamp=datetime.now()
        )
    
    def _adaptive_voting(self, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Adaptive voting that combines multiple methods"""
        # Use performance weighting if we have enough history
        if len(self.ensemble_decisions) > 10:
            return self._performance_weighted_voting(predictions, start_time)
        else:
            # Fall back to confidence weighting for new systems
            return self._confidence_weighted_voting(predictions, start_time)
    
    def _create_fallback_decision(self, symbol: str, predictions: List[ModelPrediction], start_time: float) -> EnsembleDecision:
        """Create fallback decision when insufficient models respond"""
        if predictions:
            # Use the best available prediction
            best_pred = max(predictions, key=lambda p: p.confidence)
            return EnsembleDecision(
                final_action=best_pred.action,
                ensemble_confidence=best_pred.confidence * 0.5,  # Reduce confidence due to lack of consensus
                individual_predictions=predictions,
                voting_method=VotingMethod.MAJORITY,
                consensus_level=0.5,
                conflicting_signals=False,
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
        else:
            # No predictions available
            return EnsembleDecision(
                final_action='HOLD',
                ensemble_confidence=0.0,
                individual_predictions=[],
                voting_method=VotingMethod.MAJORITY,
                consensus_level=0.0,
                conflicting_signals=False,
                execution_time=time.time() - start_time,
                timestamp=datetime.now()
            )
    
    def _update_model_performance(self, predictions: List[ModelPrediction]):
        """Update model performance tracking"""
        for pred in predictions:
            # Find corresponding performance tracker
            for model_id, perf in self.model_performance.items():
                if perf.model_name == pred.model_name:
                    perf.total_predictions += 1
                    perf.avg_response_time = (
                        perf.avg_response_time * 0.9 + pred.response_time * 0.1
                    )
                    perf.avg_confidence = (
                        perf.avg_confidence * 0.9 + pred.confidence * 0.1
                    )
                    break
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_ensemble_decisions': len(self.ensemble_decisions),
            'model_performance': {
                model_id: asdict(perf) 
                for model_id, perf in self.model_performance.items()
            },
            'recent_decisions': [
                asdict(decision) for decision in self.ensemble_decisions[-5:]
            ],
            'voting_method_usage': Counter([
                d.voting_method.value for d in self.ensemble_decisions
            ]),
            'average_consensus': np.mean([
                d.consensus_level for d in self.ensemble_decisions
            ]) if self.ensemble_decisions else 0,
            'average_execution_time': np.mean([
                d.execution_time for d in self.ensemble_decisions
            ]) if self.ensemble_decisions else 0
        }

async def main():
    """Test the advanced ensemble voting system"""
    print("ADVANCED ENSEMBLE VOTING SYSTEM - PHASE 3B")
    print("=" * 60)
    
    # Initialize ensemble
    ensemble = LocalEnsembleVotingSystem()
    
    # Test symbols
    test_symbols = ["BTC", "ETH"]
    
    for symbol in test_symbols:
        print(f"\n🧪 Testing ensemble prediction for {symbol}...")
        
        # Market context
        market_context = {
            "market_trend": "bullish",
            "volatility": "medium",
            "volume": "high",
            "news_sentiment": "positive"
        }
        
        # Get ensemble decision
        decision = await ensemble.get_ensemble_prediction(
            symbol, market_context, VotingMethod.ADAPTIVE
        )
        
        print(f"\n📊 ENSEMBLE RESULT:")
        print(f"   Action: {decision.final_action}")
        print(f"   Confidence: {decision.ensemble_confidence:.2f}")
        print(f"   Consensus: {decision.consensus_level:.2f}")
        print(f"   Models used: {len(decision.individual_predictions)}")
        print(f"   Conflicting signals: {decision.conflicting_signals}")
    
    # Performance report
    print(f"\n📈 PERFORMANCE REPORT:")
    report = ensemble.get_performance_report()
    print(f"   Total decisions: {report['total_ensemble_decisions']}")
    print(f"   Average consensus: {report['average_consensus']:.2f}")
    print(f"   Average execution time: {report['average_execution_time']:.1f}s")
    
    print(f"\n✅ Advanced Ensemble Voting System operational!")

if __name__ == "__main__":
    asyncio.run(main())
