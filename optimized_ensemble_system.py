#!/usr/bin/env python3
"""
OPTIMIZED LOCAL ENSEMBLE VOTING SYSTEM
Enhanced performance, caching, better error handling, and more models
"""

import asyncio
import concurrent.futures
import time
import yaml
import re
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
import logging
import threading
from pathlib import Path

# Import enhanced local components
from optimized_model_caller import EnhancedLocalModelCaller, ModelResponse, ModelStatus

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CachedPrediction:
    """Cached prediction with expiry"""
    prediction: Dict[str, Any]
    timestamp: datetime
    cache_key: str
    ttl_seconds: int = 300  # 5 minutes default

    def is_expired(self) -> bool:
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl_seconds)

@dataclass
class ModelPerformanceMetrics:
    """Enhanced model performance tracking"""
    model_name: str
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    total_response_time: float = 0.0
    avg_response_time: float = 0.0
    success_rate: float = 0.0
    confidence_scores: List[float] = None
    avg_confidence: float = 0.0
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.confidence_scores is None:
            self.confidence_scores = []
        if self.last_updated is None:
            self.last_updated = datetime.now()

class OptimizedEnsembleVotingSystem:
    """Optimized Local Ensemble Voting System with caching, performance tracking, and enhanced features"""
    
    def __init__(self, config_path: str = "config/ensemble_config.yaml"):
        self.model_caller = EnhancedLocalModelCaller()
        self.config_path = config_path
        self.config = self._load_config()
        
        # Performance optimization settings (initialize first)
        self.cache_enabled = self.config.get('ensemble', {}).get('cache_enabled', True)
        self.cache_ttl = self.config.get('ensemble', {}).get('cache_ttl', 300)
        self.max_parallel_models = self.config.get('ensemble', {}).get('max_parallel_models', 8)
        self.timeout_per_model = self.config.get('ensemble', {}).get('timeout_per_model', 45)

        # Enhanced features (initialize after settings)
        self.models = self._load_models_from_config()
        self.model_performance = {}
        self.voting_history = deque(maxlen=1000)  # Keep last 1000 decisions
        self.prediction_cache = {}
        self.cache_lock = threading.Lock()
        
        # Initialize performance tracking
        self._initialize_performance_tracking()
        
        logger.info(f"🚀 Optimized Ensemble initialized with {len(self.models)} models")
        logger.info(f"   Cache: {'Enabled' if self.cache_enabled else 'Disabled'}")
        logger.info(f"   Max parallel: {self.max_parallel_models}")
        logger.info(f"   Timeout per model: {self.timeout_per_model}s")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load ensemble configuration with enhanced defaults"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
                
            # Add optimization defaults if missing
            ensemble_config = config.get('ensemble', {})
            ensemble_config.setdefault('cache_enabled', True)
            ensemble_config.setdefault('cache_ttl', 300)
            ensemble_config.setdefault('max_parallel_models', 8)
            ensemble_config.setdefault('timeout_per_model', 45)
            ensemble_config.setdefault('adaptive_timeouts', True)
            ensemble_config.setdefault('performance_tracking', True)
            
            return config
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Enhanced default configuration"""
        return {
            'ensemble': {
                'enabled': True,
                'confidence_threshold': 0.7,
                'voting_strategy': 'adaptive',
                'cache_enabled': True,
                'cache_ttl': 300,
                'max_parallel_models': 8,
                'timeout_per_model': 45,
                'adaptive_timeouts': True,
                'performance_tracking': True,
                'models': []
            }
        }
    
    def _load_models_from_config(self) -> Dict[str, Dict[str, Any]]:
        """Load enhanced model configurations with more models"""
        models = {}
        
        for model_config in self.config.get('ensemble', {}).get('models', []):
            name = model_config.get('name')
            if name:
                models[name] = {
                    'specialization': model_config.get('specialization', 'general'),
                    'weight': model_config.get('weight', 0.1),
                    'confidence_threshold': model_config.get('confidence_threshold', 0.6),
                    'priority': model_config.get('priority', 5),
                    'type': model_config.get('type', 'ollama'),
                    'timeout': model_config.get('timeout', self.timeout_per_model),
                    'enabled': model_config.get('enabled', True)
                }
        
        # Enhanced fallback models with more variety
        if not models:
            models = self._get_enhanced_fallback_models()
        
        # Filter only enabled models
        return {k: v for k, v in models.items() if v.get('enabled', True)}
    
    def _get_enhanced_fallback_models(self) -> Dict[str, Dict[str, Any]]:
        """Enhanced model configuration with 10+ specialized models"""
        return {
            # Core reasoning and analysis models
            "marco-o1:latest": {
                'specialization': 'reasoning_analysis',
                'weight': 0.15,
                'confidence_threshold': 0.75,
                'priority': 1,
                'type': 'ollama',
                'timeout': 45,
                'enabled': True
            },
            "unrestricted-noryon-deepseek-r1-finance-v2-latest:latest": {
                'specialization': 'risk_assessment',
                'weight': 0.14,
                'confidence_threshold': 0.70,
                'priority': 2,
                'type': 'ollama',
                'timeout': 50,
                'enabled': True
            },
            "unrestricted-noryon-phi-4-9b-finance-latest:latest": {
                'specialization': 'technical_analysis',
                'weight': 0.13,
                'confidence_threshold': 0.70,
                'priority': 3,
                'type': 'ollama',
                'timeout': 35,
                'enabled': True
            },
            "unrestricted-noryon-qwen3-finance-v2-latest:latest": {
                'specialization': 'market_analysis',
                'weight': 0.12,
                'confidence_threshold': 0.65,
                'priority': 4,
                'type': 'ollama',
                'timeout': 40,
                'enabled': True
            },
            "unrestricted-noryon-gemma-3-12b-finance-latest:latest": {
                'specialization': 'fundamental_analysis',
                'weight': 0.11,
                'confidence_threshold': 0.65,
                'priority': 5,
                'type': 'ollama',
                'timeout': 45,
                'enabled': True
            },
            "unrestricted-noryon-cogito-finance-v2-latest:latest": {
                'specialization': 'pattern_recognition',
                'weight': 0.10,
                'confidence_threshold': 0.60,
                'priority': 6,
                'type': 'ollama',
                'timeout': 40,
                'enabled': True
            },
            # Additional specialized models
            "unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest": {
                'specialization': 'advanced_reasoning',
                'weight': 0.09,
                'confidence_threshold': 0.70,
                'priority': 7,
                'type': 'ollama',
                'timeout': 55,
                'enabled': True
            },
            "unrestricted-noryon-exaone-deep-finance-v2-latest:latest": {
                'specialization': 'deep_analysis',
                'weight': 0.08,
                'confidence_threshold': 0.65,
                'priority': 8,
                'type': 'ollama',
                'timeout': 40,
                'enabled': True
            },
            "unrestricted-noryon-falcon3-finance-v1-latest:latest": {
                'specialization': 'momentum_analysis',
                'weight': 0.07,
                'confidence_threshold': 0.60,
                'priority': 9,
                'type': 'ollama',
                'timeout': 35,
                'enabled': True
            },
            "unrestricted-noryon-deepscaler-finance-v2-latest:latest": {
                'specialization': 'scalability_analysis',
                'weight': 0.06,
                'confidence_threshold': 0.60,
                'priority': 10,
                'type': 'ollama',
                'timeout': 30,
                'enabled': True
            }
        }
    
    def _initialize_performance_tracking(self):
        """Initialize performance tracking for all models"""
        for model_name in self.models.keys():
            self.model_performance[model_name] = ModelPerformanceMetrics(model_name=model_name)
    
    def _generate_cache_key(self, symbol: str, market_context: Dict[str, Any] = None) -> str:
        """Generate cache key for prediction"""
        context_str = json.dumps(market_context or {}, sort_keys=True)
        cache_input = f"{symbol}:{context_str}"
        return hashlib.md5(cache_input.encode()).hexdigest()
    
    def _get_cached_prediction(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached prediction if valid"""
        if not self.cache_enabled:
            return None
            
        with self.cache_lock:
            cached = self.prediction_cache.get(cache_key)
            if cached and not cached.is_expired():
                logger.info(f"📋 Cache hit for key: {cache_key[:8]}...")
                return cached.prediction
            elif cached:
                # Remove expired cache
                del self.prediction_cache[cache_key]
        
        return None
    
    def _cache_prediction(self, cache_key: str, prediction: Dict[str, Any]):
        """Cache prediction with TTL"""
        if not self.cache_enabled:
            return
            
        with self.cache_lock:
            self.prediction_cache[cache_key] = CachedPrediction(
                prediction=prediction,
                timestamp=datetime.now(),
                cache_key=cache_key,
                ttl_seconds=self.cache_ttl
            )
            
            # Clean old cache entries (keep last 100)
            if len(self.prediction_cache) > 100:
                oldest_keys = sorted(
                    self.prediction_cache.keys(),
                    key=lambda k: self.prediction_cache[k].timestamp
                )[:50]
                for key in oldest_keys:
                    del self.prediction_cache[key]
    
    async def get_ensemble_prediction(self, symbol: str, market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get optimized ensemble prediction with caching and performance tracking"""
        start_time = time.time()
        
        # Check cache first
        cache_key = self._generate_cache_key(symbol, market_context)
        cached_result = self._get_cached_prediction(cache_key)
        if cached_result:
            cached_result['cached'] = True
            cached_result['cache_key'] = cache_key[:8]
            return cached_result
        
        logger.info(f"🧠 Optimized Ensemble Analysis: {symbol}")
        logger.info(f"   Models available: {len(self.models)}")
        logger.info(f"   Max parallel: {self.max_parallel_models}")
        
        # Generate enhanced prompt
        prompt = self._create_enhanced_prompt(symbol, market_context)
        
        # Get predictions with optimized parallel execution
        predictions = await self._get_optimized_parallel_predictions(prompt, symbol)
        
        # Filter successful predictions
        valid_predictions = [p for p in predictions if p['success']]
        
        if len(valid_predictions) < 2:
            logger.warning(f"Insufficient models responded ({len(valid_predictions)})")
            result = self._create_fallback_decision(symbol, valid_predictions, start_time)
        else:
            # Apply enhanced ensemble voting
            result = self._apply_enhanced_ensemble_voting(valid_predictions, start_time)
        
        # Cache the result
        self._cache_prediction(cache_key, result)
        
        # Add metadata
        result['cached'] = False
        result['cache_key'] = cache_key[:8]
        result['optimization_enabled'] = True
        
        # Store in voting history
        self.voting_history.append({
            'symbol': symbol,
            'timestamp': datetime.now(),
            'result': result,
            'market_context': market_context
        })
        
        logger.info(f"📊 OPTIMIZED ENSEMBLE RESULT:")
        logger.info(f"   Action: {result.get('final_action', 'HOLD')}")
        logger.info(f"   Confidence: {result.get('ensemble_confidence', 0.0):.2f}")
        logger.info(f"   Models used: {len(valid_predictions)}")
        logger.info(f"   Response time: {result.get('response_time', 0.0):.1f}s")
        
        return result

    def _create_enhanced_prompt(self, symbol: str, market_context: Dict[str, Any] = None) -> str:
        """Create enhanced prompt with better structure and context"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_prompt = f"""
TRADING ANALYSIS REQUEST
Symbol: {symbol}
Timestamp: {timestamp}

REQUIRED OUTPUT FORMAT:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation]

ANALYSIS REQUIREMENTS:
1. Technical indicators and price action
2. Market sentiment and momentum
3. Risk/reward assessment
4. Entry/exit strategy

MARKET CONTEXT:"""

        if market_context:
            for key, value in market_context.items():
                base_prompt += f"\n- {key.replace('_', ' ').title()}: {value}"
        else:
            base_prompt += "\n- No specific context provided"

        base_prompt += f"""

INSTRUCTIONS:
- Provide clear, actionable analysis
- Be specific about confidence level
- Consider current market conditions
- Focus on {symbol} specifically

RESPOND EXACTLY IN THIS FORMAT:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [Your analysis]
"""

        return base_prompt

    async def _get_optimized_parallel_predictions(self, prompt: str, symbol: str) -> List[Dict[str, Any]]:
        """Optimized parallel prediction execution with adaptive timeouts"""
        predictions = []

        # Sort models by priority and performance
        sorted_models = self._get_prioritized_models()

        # Use optimized ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_parallel_models) as executor:
            future_to_model = {}

            for model_name, model_config in sorted_models:
                # Adaptive timeout based on model performance
                timeout = self._get_adaptive_timeout(model_name)
                future = executor.submit(self._call_single_model_optimized, model_name, prompt, timeout)
                future_to_model[future] = model_name

            # Collect results with timeout
            for future in concurrent.futures.as_completed(future_to_model, timeout=120):
                model_name = future_to_model[future]
                try:
                    result = future.result()

                    # Update performance metrics
                    self._update_model_performance(model_name, result)

                    predictions.append({
                        'model_name': model_name,
                        'success': result.success,
                        'response': result.response,
                        'response_time': result.response_time,
                        'specialization': self.models[model_name]['specialization'],
                        'weight': self.models[model_name]['weight'],
                        'confidence_threshold': self.models[model_name]['confidence_threshold']
                    })
                except Exception as e:
                    logger.error(f"Model {model_name} failed: {e}")
                    self._update_model_performance(model_name, None, error=str(e))
                    predictions.append({
                        'model_name': model_name,
                        'success': False,
                        'response': '',
                        'response_time': 0.0,
                        'error': str(e),
                        'specialization': self.models[model_name]['specialization'],
                        'weight': self.models[model_name]['weight']
                    })

        return predictions

    def _get_prioritized_models(self) -> List[Tuple[str, Dict[str, Any]]]:
        """Get models sorted by priority and performance"""
        model_items = list(self.models.items())

        # Sort by priority first, then by success rate
        def sort_key(item):
            model_name, config = item
            priority = config.get('priority', 5)
            performance = self.model_performance.get(model_name)
            success_rate = performance.success_rate if performance else 0.5
            return (priority, -success_rate)  # Lower priority number = higher priority

        return sorted(model_items, key=sort_key)

    def _get_adaptive_timeout(self, model_name: str) -> int:
        """Get adaptive timeout based on model performance"""
        if not self.config.get('ensemble', {}).get('adaptive_timeouts', True):
            return self.models[model_name].get('timeout', self.timeout_per_model)

        performance = self.model_performance.get(model_name)
        base_timeout = self.models[model_name].get('timeout', self.timeout_per_model)

        if performance and performance.total_calls > 5:
            # Adjust timeout based on average response time
            avg_time = performance.avg_response_time
            if avg_time > 0:
                # Add 50% buffer to average response time, but cap at 2x base timeout
                adaptive_timeout = min(int(avg_time * 1.5), base_timeout * 2)
                return max(adaptive_timeout, 20)  # Minimum 20 seconds

        return base_timeout

    def _call_single_model_optimized(self, model_name: str, prompt: str, timeout: int) -> ModelResponse:
        """Optimized single model call with enhanced error handling"""
        try:
            start_time = time.time()

            # Call model with custom timeout
            response = self.model_caller.call_model_safe(model_name, prompt, timeout)

            # Add timing information
            if response.success:
                response.response_time = time.time() - start_time

            return response

        except Exception as e:
            logger.error(f"Error calling {model_name}: {e}")
            return ModelResponse(
                success=False,
                response="",
                response_time=time.time() - start_time if 'start_time' in locals() else 0.0,
                model_name=model_name,
                status=ModelStatus.ERROR,
                error_message=str(e)
            )

    def _update_model_performance(self, model_name: str, result: Optional[ModelResponse], error: str = None):
        """Update model performance metrics"""
        if model_name not in self.model_performance:
            self.model_performance[model_name] = ModelPerformanceMetrics(model_name=model_name)

        metrics = self.model_performance[model_name]
        metrics.total_calls += 1
        metrics.last_updated = datetime.now()

        if result and result.success:
            metrics.successful_calls += 1
            metrics.total_response_time += result.response_time
            metrics.avg_response_time = metrics.total_response_time / metrics.successful_calls

            # Extract confidence if available
            try:
                confidence = self._extract_confidence_from_response(result.response)
                if confidence is not None:
                    metrics.confidence_scores.append(confidence)
                    metrics.avg_confidence = sum(metrics.confidence_scores) / len(metrics.confidence_scores)
            except:
                pass
        else:
            metrics.failed_calls += 1

        metrics.success_rate = metrics.successful_calls / metrics.total_calls if metrics.total_calls > 0 else 0.0

    def _extract_confidence_from_response(self, response: str) -> Optional[float]:
        """Extract confidence score from model response"""
        try:
            conf_match = re.search(r'CONFIDENCE:\s*([0-9.]+)', response.upper())
            if conf_match:
                return float(conf_match.group(1))
        except:
            pass
        return None

    def _apply_enhanced_ensemble_voting(self, predictions: List[Dict[str, Any]], start_time: float) -> Dict[str, Any]:
        """Enhanced ensemble voting with advanced algorithms"""
        if not predictions:
            return self._create_empty_decision(start_time)

        # Parse predictions with enhanced extraction
        parsed_predictions = []
        for pred in predictions:
            parsed = self._parse_prediction_enhanced(pred)
            if parsed:
                parsed_predictions.append(parsed)

        if not parsed_predictions:
            return self._create_empty_decision(start_time)

        # Apply multiple voting strategies and combine
        strategies = {
            'weighted': self._weighted_voting(parsed_predictions),
            'confidence_weighted': self._confidence_weighted_voting(parsed_predictions),
            'performance_weighted': self._performance_weighted_voting(parsed_predictions)
        }

        # Meta-voting: combine strategies
        final_decision = self._meta_voting(strategies, parsed_predictions, start_time)

        return final_decision

    def _weighted_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Standard weighted voting"""
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        for pred in predictions:
            weight = pred['weight']
            action_votes[pred['action']] += weight * pred['confidence']
            confidence_sum += pred['confidence'] * weight
            total_weight += weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'weighted'
        }

    def _confidence_weighted_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Confidence-weighted voting"""
        action_votes = defaultdict(float)
        total_confidence = 0.0

        for pred in predictions:
            confidence_weight = pred['confidence'] ** 2  # Square confidence for emphasis
            action_votes[pred['action']] += confidence_weight
            total_confidence += confidence_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = action_votes[final_action] / total_confidence if total_confidence > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'confidence_weighted'
        }

    def _performance_weighted_voting(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Performance-weighted voting based on historical accuracy"""
        action_votes = defaultdict(float)
        confidence_sum = 0.0
        total_weight = 0.0

        for pred in predictions:
            model_name = pred['model_name']
            performance = self.model_performance.get(model_name)

            # Calculate performance weight
            if performance and performance.total_calls > 5:
                perf_weight = performance.success_rate * pred['weight']
            else:
                perf_weight = pred['weight'] * 0.5  # Default for new models

            action_votes[pred['action']] += perf_weight * pred['confidence']
            confidence_sum += pred['confidence'] * perf_weight
            total_weight += perf_weight

        final_action = max(action_votes.items(), key=lambda x: x[1])[0] if action_votes else 'HOLD'
        ensemble_confidence = confidence_sum / total_weight if total_weight > 0 else 0.0

        return {
            'action': final_action,
            'confidence': ensemble_confidence,
            'method': 'performance_weighted'
        }

    def _meta_voting(self, strategies: Dict[str, Dict[str, Any]], predictions: List[Dict[str, Any]], start_time: float) -> Dict[str, Any]:
        """Meta-voting: combine multiple voting strategies"""

        # Count votes from each strategy
        action_counts = defaultdict(int)
        confidence_sum = 0.0

        for strategy_name, result in strategies.items():
            action_counts[result['action']] += 1
            confidence_sum += result['confidence']

        # Final action is majority vote from strategies
        final_action = max(action_counts.items(), key=lambda x: x[1])[0] if action_counts else 'HOLD'

        # Average confidence from strategies that voted for final action
        matching_strategies = [s for s in strategies.values() if s['action'] == final_action]
        ensemble_confidence = sum(s['confidence'] for s in matching_strategies) / len(matching_strategies) if matching_strategies else 0.0

        # Calculate consensus level
        consensus_level = len([p for p in predictions if p['action'] == final_action]) / len(predictions)

        return {
            'final_action': final_action,
            'ensemble_confidence': ensemble_confidence,
            'individual_predictions': predictions,
            'consensus_level': consensus_level,
            'response_time': time.time() - start_time,
            'models_used': len(predictions),
            'voting_strategies': strategies,
            'meta_voting': True
        }

    def _parse_prediction_enhanced(self, prediction: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Enhanced prediction parsing with better extraction"""
        try:
            response = prediction['response'].upper()

            # Enhanced action extraction
            action = 'HOLD'  # Default
            if 'ACTION:' in response:
                action_match = re.search(r'ACTION:\s*([A-Z]+)', response)
                if action_match:
                    action = action_match.group(1)
            elif 'BUY' in response and 'SELL' not in response:
                action = 'BUY'
            elif 'SELL' in response and 'BUY' not in response:
                action = 'SELL'

            # Enhanced confidence extraction
            confidence = 0.5  # Default
            conf_patterns = [
                r'CONFIDENCE:\s*([0-9.]+)',
                r'CONFIDENCE\s*=\s*([0-9.]+)',
                r'([0-9.]+)\s*CONFIDENCE',
                r'CONF:\s*([0-9.]+)'
            ]

            for pattern in conf_patterns:
                match = re.search(pattern, response)
                if match:
                    conf_value = float(match.group(1))
                    confidence = conf_value if conf_value <= 1.0 else conf_value / 10.0
                    break

            return {
                'model_name': prediction['model_name'],
                'action': action,
                'confidence': confidence,
                'weight': prediction['weight'],
                'specialization': prediction['specialization'],
                'response_time': prediction['response_time'],
                'raw_response': prediction['response'][:200] + "..." if len(prediction['response']) > 200 else prediction['response']
            }
        except Exception as e:
            logger.error(f"Error parsing prediction: {e}")
            return None

    def _create_empty_decision(self, start_time: float) -> Dict[str, Any]:
        """Create empty decision when no valid predictions"""
        return {
            'final_action': 'HOLD',
            'ensemble_confidence': 0.0,
            'individual_predictions': [],
            'consensus_level': 0.0,
            'response_time': time.time() - start_time,
            'models_used': 0,
            'error': 'No valid predictions available',
            'meta_voting': False
        }

    def _create_fallback_decision(self, symbol: str, predictions: List[Dict[str, Any]], start_time: float) -> Dict[str, Any]:
        """Create fallback decision when insufficient models respond"""
        if predictions:
            # Use the best available prediction
            best_pred = max(predictions, key=lambda x: x.get('confidence', 0.0))
            parsed = self._parse_prediction_enhanced(best_pred)

            if parsed:
                return {
                    'final_action': parsed['action'],
                    'ensemble_confidence': parsed['confidence'] * 0.5,  # Reduced confidence
                    'individual_predictions': [parsed],
                    'consensus_level': 0.0,
                    'response_time': time.time() - start_time,
                    'models_used': 1,
                    'fallback': True,
                    'meta_voting': False
                }

        return self._create_empty_decision(start_time)

    # Utility and monitoring methods
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(self.models),
            'cache_enabled': self.cache_enabled,
            'cache_size': len(self.prediction_cache),
            'voting_history_size': len(self.voting_history),
            'model_performance': {}
        }

        for model_name, metrics in self.model_performance.items():
            report['model_performance'][model_name] = {
                'total_calls': metrics.total_calls,
                'success_rate': metrics.success_rate,
                'avg_response_time': metrics.avg_response_time,
                'avg_confidence': metrics.avg_confidence,
                'last_updated': metrics.last_updated.isoformat() if metrics.last_updated else None
            }

        return report

    def get_model_rankings(self) -> List[Dict[str, Any]]:
        """Get models ranked by performance"""
        rankings = []

        for model_name, metrics in self.model_performance.items():
            if metrics.total_calls > 0:
                # Calculate composite score
                score = (metrics.success_rate * 0.4 +
                        (1.0 / max(metrics.avg_response_time, 1.0)) * 0.3 +
                        metrics.avg_confidence * 0.3)

                rankings.append({
                    'model_name': model_name,
                    'score': score,
                    'success_rate': metrics.success_rate,
                    'avg_response_time': metrics.avg_response_time,
                    'avg_confidence': metrics.avg_confidence,
                    'total_calls': metrics.total_calls,
                    'specialization': self.models[model_name]['specialization']
                })

        return sorted(rankings, key=lambda x: x['score'], reverse=True)

    def clear_cache(self):
        """Clear prediction cache"""
        with self.cache_lock:
            self.prediction_cache.clear()
        logger.info("🗑️ Prediction cache cleared")

    def optimize_model_weights(self):
        """Optimize model weights based on performance"""
        rankings = self.get_model_rankings()

        if len(rankings) < 2:
            return

        # Redistribute weights based on performance
        total_score = sum(r['score'] for r in rankings)

        for ranking in rankings:
            model_name = ranking['model_name']
            if model_name in self.models:
                # New weight based on performance score
                new_weight = (ranking['score'] / total_score) * 0.8  # 80% based on performance
                old_weight = self.models[model_name]['weight']

                # Smooth transition (20% old weight + 80% new weight)
                self.models[model_name]['weight'] = old_weight * 0.2 + new_weight * 0.8

        logger.info("⚖️ Model weights optimized based on performance")

    async def health_check(self) -> Dict[str, Any]:
        """Perform system health check"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'models_status': {},
            'cache_status': 'enabled' if self.cache_enabled else 'disabled',
            'performance_summary': {}
        }

        # Quick test of top 3 models
        top_models = self.get_model_rankings()[:3]
        test_prompt = "Quick health check. Respond: ACTION: HOLD, CONFIDENCE: 0.5"

        for model_info in top_models:
            model_name = model_info['model_name']
            try:
                start_time = time.time()
                response = self.model_caller.call_model_safe(model_name, test_prompt, timeout=15)
                response_time = time.time() - start_time

                health_status['models_status'][model_name] = {
                    'status': 'healthy' if response.success else 'unhealthy',
                    'response_time': response_time,
                    'error': response.error_message if not response.success else None
                }
            except Exception as e:
                health_status['models_status'][model_name] = {
                    'status': 'error',
                    'error': str(e)
                }

        # Overall health assessment
        healthy_models = sum(1 for status in health_status['models_status'].values()
                           if status['status'] == 'healthy')

        if healthy_models == 0:
            health_status['overall_status'] = 'critical'
        elif healthy_models < len(health_status['models_status']) // 2:
            health_status['overall_status'] = 'degraded'

        return health_status

# Test and demo functions
async def test_optimized_ensemble():
    """Test the optimized ensemble system"""
    print("🚀 OPTIMIZED ENSEMBLE VOTING SYSTEM TEST")
    print("=" * 70)

    # Initialize optimized ensemble
    ensemble = OptimizedEnsembleVotingSystem()

    # Performance report
    print("\n📊 Initial Performance Report:")
    report = ensemble.get_performance_report()
    print(f"   Total Models: {report['total_models']}")
    print(f"   Cache Enabled: {report['cache_enabled']}")

    # Health check
    print("\n🏥 System Health Check:")
    health = await ensemble.health_check()
    print(f"   Overall Status: {health['overall_status']}")
    print(f"   Models Tested: {len(health['models_status'])}")

    # Test predictions
    test_cases = [
        {"symbol": "AAPL", "context": {"trend": "bullish", "volatility": "medium"}},
        {"symbol": "TSLA", "context": {"trend": "volatile", "volatility": "high"}},
        {"symbol": "BTC", "context": {"trend": "bullish", "volatility": "high"}}
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 TEST {i}: {test_case['symbol']}")
        print("-" * 40)

        start_time = time.time()
        decision = await ensemble.get_ensemble_prediction(test_case['symbol'], test_case['context'])
        total_time = time.time() - start_time

        print(f"   Action: {decision.get('final_action', 'UNKNOWN')}")
        print(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
        print(f"   Consensus: {decision.get('consensus_level', 0.0):.2f}")
        print(f"   Models Used: {decision.get('models_used', 0)}")
        print(f"   Response Time: {total_time:.1f}s")
        print(f"   Cached: {decision.get('cached', False)}")
        print(f"   Meta Voting: {decision.get('meta_voting', False)}")

    # Test caching (repeat first test)
    print(f"\n🔄 CACHE TEST: Repeating {test_cases[0]['symbol']}")
    print("-" * 40)
    start_time = time.time()
    cached_decision = await ensemble.get_ensemble_prediction(test_cases[0]['symbol'], test_cases[0]['context'])
    cache_time = time.time() - start_time
    print(f"   Cached: {cached_decision.get('cached', False)}")
    print(f"   Cache Response Time: {cache_time:.3f}s")

    # Final performance report
    print("\n📈 Final Performance Report:")
    final_report = ensemble.get_performance_report()
    print(f"   Cache Size: {final_report['cache_size']}")
    print(f"   Voting History: {final_report['voting_history_size']}")

    # Model rankings
    print("\n🏆 Model Performance Rankings:")
    rankings = ensemble.get_model_rankings()
    for i, model in enumerate(rankings[:5], 1):
        print(f"   {i}. {model['model_name'][:40]}...")
        print(f"      Score: {model['score']:.3f}")
        print(f"      Success Rate: {model['success_rate']:.2f}")
        print(f"      Avg Time: {model['avg_response_time']:.1f}s")

    print("\n✅ Optimized Ensemble System test completed!")

if __name__ == "__main__":
    asyncio.run(test_optimized_ensemble())
