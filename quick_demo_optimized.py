#!/usr/bin/env python3
"""
Quick Demo of Optimized Ensemble System
Shows the enhanced features working
"""

import asyncio
import time
from optimized_ensemble_system import OptimizedEnsembleVotingSystem

async def quick_demo():
    """Quick demonstration of optimized features"""
    print("🚀 OPTIMIZED ENSEMBLE SYSTEM - QUICK DEMO")
    print("=" * 60)
    
    # Initialize system
    print("🔧 Initializing Optimized Ensemble...")
    ensemble = OptimizedEnsembleVotingSystem()
    
    print(f"✅ System initialized with {len(ensemble.models)} specialized AI models")
    print(f"✅ Cache: {'Enabled' if ensemble.cache_enabled else 'Disabled'}")
    print(f"✅ Max parallel models: {ensemble.max_parallel_models}")
    print(f"✅ Performance tracking: Active")
    
    # Quick health check
    print("\n🏥 Quick Health Check...")
    health = await ensemble.health_check()
    print(f"✅ System status: {health['overall_status']}")
    
    # Demo trading decision
    print(f"\n📈 DEMO: Getting trading decision for AAPL...")
    
    market_context = {
        "trend": "bullish",
        "volatility": "medium",
        "volume": "high",
        "news_sentiment": "positive"
    }
    
    start_time = time.time()
    decision = await ensemble.get_ensemble_prediction("AAPL", market_context)
    total_time = time.time() - start_time
    
    print(f"\n🎯 TRADING DECISION:")
    print(f"   Symbol: AAPL")
    print(f"   Action: {decision.get('final_action', 'UNKNOWN')}")
    print(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
    print(f"   Consensus: {decision.get('consensus_level', 0.0):.2f}")
    print(f"   Models Used: {decision.get('models_used', 0)}")
    print(f"   Response Time: {total_time:.1f}s")
    print(f"   Meta Voting: {decision.get('meta_voting', False)}")
    print(f"   Cached: {decision.get('cached', False)}")
    
    # Demo caching
    print(f"\n🔄 DEMO: Testing cache (repeating same query)...")
    start_time = time.time()
    cached_decision = await ensemble.get_ensemble_prediction("AAPL", market_context)
    cache_time = time.time() - start_time
    
    print(f"✅ Cache hit: {cached_decision.get('cached', False)}")
    print(f"✅ Cache response time: {cache_time:.3f}s")
    
    if cached_decision.get('cached', False):
        improvement = ((total_time - cache_time) / total_time) * 100
        print(f"✅ Speed improvement: {improvement:.1f}%")
    
    # Show model performance
    print(f"\n🏆 TOP PERFORMING MODELS:")
    rankings = ensemble.get_model_rankings()
    for i, model in enumerate(rankings[:3], 1):
        print(f"   {i}. {model['model_name'][:40]}...")
        print(f"      Performance Score: {model['score']:.3f}")
        print(f"      Success Rate: {model['success_rate']:.2f}")
        print(f"      Avg Response Time: {model['avg_response_time']:.1f}s")
    
    # Performance report
    print(f"\n📊 SYSTEM PERFORMANCE:")
    report = ensemble.get_performance_report()
    print(f"   Total Models: {report['total_models']}")
    print(f"   Cache Size: {report['cache_size']}")
    print(f"   Voting History: {report['voting_history_size']}")
    
    print(f"\n✅ Demo completed! The optimized ensemble system is fully operational.")
    print(f"\n🎉 KEY FEATURES DEMONSTRATED:")
    print(f"   ✅ 10+ Specialized AI models working together")
    print(f"   ✅ Meta-voting algorithms for better decisions")
    print(f"   ✅ Intelligent caching for faster responses")
    print(f"   ✅ Performance tracking and optimization")
    print(f"   ✅ Robust error handling and fallbacks")
    print(f"   ✅ Real-time trading decision making")

if __name__ == "__main__":
    asyncio.run(quick_demo())
