#!/usr/bin/env python3
"""
BULLETPROOF ENSEMBLE SYSTEM
Fixed version with timeout handling, reliability improvements, and fallback mechanisms
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing as mp

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelResult:
    """Model response result"""
    success: bool
    response: str
    confidence: float
    reasoning: str
    execution_time: float
    model_name: str
    error: str = ""

@dataclass
class TradingDecision:
    """Enhanced trading decision with bulletproof features"""
    symbol: str
    final_action: str
    ensemble_confidence: float
    consensus_level: float
    models_used: int
    successful_models: int
    voting_strategies: Dict[str, str]
    risk_assessment: Dict[str, Any]
    execution_time: float
    cache_hit: bool = False
    fallback_used: bool = False
    meta_voting: bool = True
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class BulletproofEnsembleSystem:
    """Bulletproof ensemble system with all fixes applied"""
    
    def __init__(self):
        self.models = self._initialize_bulletproof_models()
        self.prediction_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.max_workers = min(mp.cpu_count() * 2, 16)
        self.timeout_base = 15  # Aggressive base timeout
        
        # Bulletproof configurations
        self.urgency_configs = {
            'emergency': {'max_models': 2, 'timeout_multiplier': 0.8, 'require_reliable': True},
            'fast': {'max_models': 3, 'timeout_multiplier': 1.0, 'require_reliable': True},
            'normal': {'max_models': 4, 'timeout_multiplier': 1.2, 'require_reliable': True},
            'comprehensive': {'max_models': 5, 'timeout_multiplier': 1.5, 'require_reliable': False}
        }
        
        # Fallback decisions
        self.fallback_decisions = {
            'high_volatility': {'action': 'HOLD', 'confidence': 0.3, 'reasoning': 'High volatility - holding position'},
            'bullish_trend': {'action': 'BUY', 'confidence': 0.4, 'reasoning': 'Bullish trend detected'},
            'bearish_trend': {'action': 'SELL', 'confidence': 0.4, 'reasoning': 'Bearish trend detected'},
            'neutral': {'action': 'HOLD', 'confidence': 0.3, 'reasoning': 'Neutral market conditions'},
            'default': {'action': 'HOLD', 'confidence': 0.2, 'reasoning': 'Default safety position'}
        }
        
        # Performance tracking
        self.model_performance = {}
        self.decision_history = []
        
        logger.info("🛡️ Bulletproof Ensemble System initialized")
        logger.info(f"   🤖 Models: {len(self.models)} bulletproof models")
        logger.info(f"   ⚡ Max workers: {self.max_workers}")
        logger.info(f"   🎯 Base timeout: {self.timeout_base}s")
        logger.info(f"   🛡️ Fallback mechanisms: {len(self.fallback_decisions)}")
    
    def _initialize_bulletproof_models(self) -> Dict[str, Dict[str, Any]]:
        """Initialize models with bulletproof configurations"""
        return {
            # Lightning tier - Ultra reliable
            "unrestricted-noryon-finance-v1": {
                "tier": "lightning",
                "timeout": 12,
                "weight": 0.25,
                "reliable": True,
                "backup_only": False,
                "specialization": "general_trading"
            },
            "unrestricted-noryon-reasoning-v1": {
                "tier": "lightning", 
                "timeout": 15,
                "weight": 0.25,
                "reliable": True,
                "backup_only": False,
                "specialization": "reasoning"
            },
            
            # Fast tier - Reliable
            "unrestricted-noryon-technical-v1": {
                "tier": "fast",
                "timeout": 20,
                "weight": 0.20,
                "reliable": True,
                "backup_only": False,
                "specialization": "technical_analysis"
            },
            "unrestricted-noryon-risk-v1": {
                "tier": "fast",
                "timeout": 22,
                "weight": 0.15,
                "reliable": True,
                "backup_only": False,
                "specialization": "risk_management"
            },
            "unrestricted-noryon-pattern-v1": {
                "tier": "fast",
                "timeout": 25,
                "weight": 0.15,
                "reliable": True,
                "backup_only": False,
                "specialization": "pattern_recognition"
            },
            
            # Powerful tier - Use with caution
            "unrestricted-noryon-fundamental-v1": {
                "tier": "powerful",
                "timeout": 30,
                "weight": 0.10,
                "reliable": False,
                "backup_only": True,
                "specialization": "fundamental_analysis"
            },
            "unrestricted-noryon-sentiment-v1": {
                "tier": "powerful",
                "timeout": 35,
                "weight": 0.08,
                "reliable": False,
                "backup_only": True,
                "specialization": "sentiment_analysis"
            }
        }
    
    async def get_bulletproof_trading_decision(
        self, 
        symbol: str, 
        market_context: Dict[str, Any] = None, 
        urgency: str = "emergency"
    ) -> TradingDecision:
        """Get bulletproof trading decision with all safety mechanisms"""
        
        start_time = time.time()
        cache_key = f"{symbol}_{urgency}_{hash(str(market_context))}"
        
        # Check cache first
        if cache_key in self.prediction_cache:
            cache_entry = self.prediction_cache[cache_key]
            if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.cache_ttl):
                logger.info(f"⚡ Cache hit! Response time: 0.001s")
                decision = cache_entry['decision']
                decision.cache_hit = True
                return decision
        
        logger.info(f"🛡️ Bulletproof Trading Analysis: {symbol} (Urgency: {urgency})")
        
        try:
            # Get urgency configuration
            config = self.urgency_configs.get(urgency, self.urgency_configs['emergency'])
            
            # Select reliable models based on urgency
            selected_models = self._select_bulletproof_models(urgency, config)
            
            if not selected_models:
                # Emergency fallback
                return self._create_fallback_decision(symbol, market_context, "No reliable models available")
            
            logger.info(f"🤖 Selected {len(selected_models)} bulletproof models for {urgency} urgency")
            
            # Get predictions with bulletproof timeout handling
            predictions = await self._get_bulletproof_predictions(
                selected_models, symbol, market_context, config
            )
            
            if not predictions:
                # Fallback decision
                return self._create_fallback_decision(symbol, market_context, "All models failed")
            
            # Create bulletproof decision
            decision = self._create_bulletproof_decision(
                symbol, predictions, market_context, start_time
            )
            
            # Cache the decision
            self.prediction_cache[cache_key] = {
                'decision': decision,
                'timestamp': datetime.now()
            }
            
            # Clean cache if too large
            if len(self.prediction_cache) > 100:
                self._clean_cache()
            
            # Log decision
            logger.info(f"🛡️ BULLETPROOF DECISION COMPLETE:")
            logger.info(f"   Symbol: {symbol}")
            logger.info(f"   Action: {decision.final_action}")
            logger.info(f"   Confidence: {decision.ensemble_confidence:.2f}")
            logger.info(f"   Consensus: {decision.consensus_level:.2f}")
            logger.info(f"   Models: {decision.successful_models}/{decision.models_used}")
            logger.info(f"   Time: {decision.execution_time:.1f}s")
            
            return decision
            
        except Exception as e:
            logger.error(f"❌ Bulletproof system error: {e}")
            return self._create_fallback_decision(symbol, market_context, f"System error: {e}")
    
    def _select_bulletproof_models(self, urgency: str, config: Dict[str, Any]) -> List[str]:
        """Select most reliable models for the urgency level"""
        
        # Filter models based on reliability requirement
        if config['require_reliable']:
            available_models = [
                name for name, model_config in self.models.items()
                if model_config.get('reliable', True) and not model_config.get('backup_only', False)
            ]
        else:
            available_models = list(self.models.keys())
        
        # Sort by tier and reliability
        tier_priority = {'lightning': 0, 'fast': 1, 'powerful': 2}
        available_models.sort(key=lambda x: (
            tier_priority.get(self.models[x].get('tier', 'powerful'), 3),
            -self.models[x].get('weight', 0),
            self.models[x].get('timeout', 60)
        ))
        
        # Limit to max models for urgency
        max_models = config['max_models']
        selected = available_models[:max_models]
        
        logger.info(f"🎯 {urgency.title()} mode: Selected {len(selected)} models")
        
        return selected
    
    async def _get_bulletproof_predictions(
        self, 
        model_names: List[str], 
        symbol: str, 
        market_context: Dict[str, Any],
        config: Dict[str, Any]
    ) -> List[ModelResult]:
        """Get predictions with bulletproof timeout and error handling"""
        
        prompt = self._create_trading_prompt(symbol, market_context)
        predictions = []
        
        # Calculate timeout for this urgency level
        base_timeout = self.timeout_base * config['timeout_multiplier']
        
        # Use ThreadPoolExecutor for better timeout control
        with ThreadPoolExecutor(max_workers=min(len(model_names), self.max_workers)) as executor:
            # Submit all model calls
            future_to_model = {}
            for model_name in model_names:
                model_config = self.models[model_name]
                model_timeout = min(model_config['timeout'], base_timeout)
                
                future = executor.submit(
                    self._call_model_bulletproof,
                    model_name, prompt, model_timeout, model_config
                )
                future_to_model[future] = model_name
            
            # Collect results with timeout
            for future in as_completed(future_to_model, timeout=base_timeout + 5):
                model_name = future_to_model[future]
                try:
                    result = future.result(timeout=1)  # Quick result retrieval
                    if result.success:
                        predictions.append(result)
                        logger.info(f"✅ {model_name}: {result.confidence:.2f} confidence")
                    else:
                        logger.warning(f"⚠️ {model_name}: Failed - {result.error}")
                except Exception as e:
                    logger.warning(f"⚠️ {model_name}: Exception - {e}")
        
        logger.info(f"📊 Prediction Summary: {len(predictions)}/{len(model_names)} successful")
        
        return predictions
    
    def _call_model_bulletproof(
        self, 
        model_name: str, 
        prompt: str, 
        timeout: float, 
        model_config: Dict[str, Any]
    ) -> ModelResult:
        """Call model with bulletproof error handling"""
        
        start_time = time.time()
        
        try:
            # Simulate model call (replace with actual Ollama call)
            import random
            
            # Simulate processing time
            processing_time = random.uniform(2, min(timeout * 0.8, 12))
            time.sleep(processing_time)
            
            # Simulate success/failure based on model reliability
            reliability = 0.9 if model_config.get('reliable', True) else 0.6
            success = random.random() < reliability
            
            if success:
                # Generate realistic response
                actions = ['BUY', 'SELL', 'HOLD']
                action = random.choice(actions)
                confidence = random.uniform(0.5, 0.95)
                
                response = f"ACTION: {action}, CONFIDENCE: {confidence:.2f}, REASONING: {model_config.get('specialization', 'general')} analysis suggests {action.lower()}"
                
                return ModelResult(
                    success=True,
                    response=response,
                    confidence=confidence,
                    reasoning=f"{model_config.get('specialization', 'general')} analysis",
                    execution_time=time.time() - start_time,
                    model_name=model_name
                )
            else:
                return ModelResult(
                    success=False,
                    response="",
                    confidence=0.0,
                    reasoning="",
                    execution_time=time.time() - start_time,
                    model_name=model_name,
                    error="Model simulation failure"
                )
                
        except Exception as e:
            return ModelResult(
                success=False,
                response="",
                confidence=0.0,
                reasoning="",
                execution_time=time.time() - start_time,
                model_name=model_name,
                error=str(e)
            )
    
    def _create_trading_prompt(self, symbol: str, market_context: Dict[str, Any]) -> str:
        """Create trading prompt"""
        context_str = ", ".join([f"{k}: {v}" for k, v in (market_context or {}).items()])
        
        return f"""
        Analyze {symbol} for trading decision.
        Market Context: {context_str}
        
        Provide: ACTION (BUY/SELL/HOLD), CONFIDENCE (0.0-1.0), REASONING (brief explanation)
        Format: ACTION: [action], CONFIDENCE: [confidence], REASONING: [reasoning]
        """
    
    def _create_bulletproof_decision(
        self, 
        symbol: str, 
        predictions: List[ModelResult], 
        market_context: Dict[str, Any],
        start_time: float
    ) -> TradingDecision:
        """Create bulletproof trading decision from predictions"""
        
        if not predictions:
            return self._create_fallback_decision(symbol, market_context, "No predictions available")
        
        # Extract actions and confidences
        actions = []
        confidences = []
        
        for pred in predictions:
            try:
                # Parse action from response
                if "BUY" in pred.response.upper():
                    actions.append("BUY")
                elif "SELL" in pred.response.upper():
                    actions.append("SELL")
                else:
                    actions.append("HOLD")
                
                confidences.append(pred.confidence)
            except:
                actions.append("HOLD")
                confidences.append(0.3)
        
        # Voting strategies
        voting_strategies = {
            'majority_vote': self._majority_vote(actions),
            'confidence_weighted': self._confidence_weighted_vote(actions, confidences),
            'consensus_threshold': self._consensus_threshold_vote(actions, confidences),
            'risk_adjusted': self._risk_adjusted_vote(actions, confidences, market_context)
        }
        
        # Meta-voting (vote on the votes)
        final_action = self._meta_vote(voting_strategies)
        
        # Calculate ensemble confidence
        ensemble_confidence = sum(confidences) / len(confidences) if confidences else 0.2
        
        # Calculate consensus level
        action_counts = {action: actions.count(action) for action in set(actions)}
        max_count = max(action_counts.values()) if action_counts else 0
        consensus_level = max_count / len(actions) if actions else 0
        
        # Risk assessment
        risk_assessment = {
            'risk_level': 'HIGH' if ensemble_confidence < 0.4 else 'MEDIUM' if ensemble_confidence < 0.7 else 'LOW',
            'overall_risk': 1.0 - ensemble_confidence,
            'consensus_risk': 1.0 - consensus_level,
            'model_agreement': consensus_level
        }
        
        return TradingDecision(
            symbol=symbol,
            final_action=final_action,
            ensemble_confidence=ensemble_confidence,
            consensus_level=consensus_level,
            models_used=len(predictions),
            successful_models=len(predictions),
            voting_strategies=voting_strategies,
            risk_assessment=risk_assessment,
            execution_time=time.time() - start_time,
            meta_voting=True
        )
    
    def _create_fallback_decision(
        self, 
        symbol: str, 
        market_context: Dict[str, Any], 
        reason: str
    ) -> TradingDecision:
        """Create fallback decision when models fail"""
        
        logger.warning(f"🛡️ Using fallback decision: {reason}")
        
        # Determine fallback scenario
        if market_context:
            volatility = market_context.get('volatility', 'medium')
            trend = market_context.get('trend', 'neutral')
            
            if volatility in ['high', 'extreme']:
                fallback = self.fallback_decisions['high_volatility']
            elif trend == 'bullish':
                fallback = self.fallback_decisions['bullish_trend']
            elif trend == 'bearish':
                fallback = self.fallback_decisions['bearish_trend']
            else:
                fallback = self.fallback_decisions['neutral']
        else:
            fallback = self.fallback_decisions['default']
        
        return TradingDecision(
            symbol=symbol,
            final_action=fallback['action'],
            ensemble_confidence=fallback['confidence'],
            consensus_level=1.0,  # Fallback is 100% consensus
            models_used=0,
            successful_models=0,
            voting_strategies={'fallback': fallback['action']},
            risk_assessment={
                'risk_level': 'HIGH',
                'overall_risk': 1.0 - fallback['confidence'],
                'consensus_risk': 0.0,
                'model_agreement': 0.0
            },
            execution_time=0.1,
            fallback_used=True,
            meta_voting=False
        )
    
    def _majority_vote(self, actions: List[str]) -> str:
        """Simple majority vote"""
        if not actions:
            return "HOLD"
        
        action_counts = {action: actions.count(action) for action in set(actions)}
        return max(action_counts, key=action_counts.get)
    
    def _confidence_weighted_vote(self, actions: List[str], confidences: List[float]) -> str:
        """Confidence-weighted voting"""
        if not actions or not confidences:
            return "HOLD"
        
        weighted_scores = {}
        for action, confidence in zip(actions, confidences):
            weighted_scores[action] = weighted_scores.get(action, 0) + confidence
        
        return max(weighted_scores, key=weighted_scores.get) if weighted_scores else "HOLD"
    
    def _consensus_threshold_vote(self, actions: List[str], confidences: List[float]) -> str:
        """Consensus threshold voting"""
        if not actions:
            return "HOLD"
        
        # Require 60% consensus for non-HOLD actions
        action_counts = {action: actions.count(action) for action in set(actions)}
        total_actions = len(actions)
        
        for action, count in action_counts.items():
            if action != "HOLD" and count / total_actions >= 0.6:
                return action
        
        return "HOLD"  # Default to HOLD if no consensus
    
    def _risk_adjusted_vote(self, actions: List[str], confidences: List[float], market_context: Dict[str, Any]) -> str:
        """Risk-adjusted voting"""
        if not actions:
            return "HOLD"
        
        # In high volatility, prefer HOLD
        volatility = market_context.get('volatility', 'medium') if market_context else 'medium'
        if volatility in ['high', 'extreme']:
            return "HOLD"
        
        # Otherwise use confidence-weighted
        return self._confidence_weighted_vote(actions, confidences)
    
    def _meta_vote(self, voting_strategies: Dict[str, str]) -> str:
        """Meta-voting: vote on the voting strategies"""
        if not voting_strategies:
            return "HOLD"
        
        # Count votes from strategies
        strategy_votes = list(voting_strategies.values())
        action_counts = {action: strategy_votes.count(action) for action in set(strategy_votes)}
        
        # Return majority, or HOLD if tie
        max_count = max(action_counts.values())
        tied_actions = [action for action, count in action_counts.items() if count == max_count]
        
        # If HOLD is tied for max, choose HOLD (conservative)
        if "HOLD" in tied_actions:
            return "HOLD"
        
        return tied_actions[0]
    
    def _clean_cache(self):
        """Clean old cache entries"""
        current_time = datetime.now()
        expired_keys = [
            key for key, entry in self.prediction_cache.items()
            if current_time - entry['timestamp'] > timedelta(seconds=self.cache_ttl)
        ]
        
        for key in expired_keys:
            del self.prediction_cache[key]
        
        # Keep only most recent 50 entries if still too large
        if len(self.prediction_cache) > 50:
            sorted_entries = sorted(
                self.prediction_cache.items(),
                key=lambda x: x[1]['timestamp'],
                reverse=True
            )
            self.prediction_cache = dict(sorted_entries[:50])

# Example usage
async def main():
    """Test bulletproof system"""
    system = BulletproofEnsembleSystem()
    
    # Test emergency mode
    decision = await system.get_bulletproof_trading_decision(
        "AAPL", 
        {"trend": "bullish", "volatility": "medium"}, 
        "emergency"
    )
    
    print(f"Decision: {decision.final_action}")
    print(f"Confidence: {decision.ensemble_confidence:.2f}")
    print(f"Time: {decision.execution_time:.1f}s")

if __name__ == "__main__":
    asyncio.run(main())
