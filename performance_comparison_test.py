#!/usr/bin/env python3
"""
Performance Comparison Test
Compare original vs optimized ensemble systems
"""

import asyncio
import time
import json
from datetime import datetime

# Import both systems
from local_ensemble_voting_system import LocalEnsembleVotingSystem
from optimized_ensemble_system import OptimizedEnsembleVotingSystem

async def performance_comparison():
    """Compare performance between original and optimized systems"""
    print("🏁 PERFORMANCE COMPARISON TEST")
    print("=" * 80)
    
    # Test cases
    test_cases = [
        {"symbol": "AAPL", "context": {"trend": "bullish", "volatility": "medium"}},
        {"symbol": "TSLA", "context": {"trend": "volatile", "volatility": "high"}},
        {"symbol": "BTC", "context": {"trend": "bullish", "volatility": "high"}},
        {"symbol": "NVDA", "context": {"trend": "bullish", "volatility": "medium"}},
        {"symbol": "MSFT", "context": {"trend": "stable", "volatility": "low"}}
    ]
    
    # Initialize systems
    print("\n🔧 Initializing Systems...")
    original_system = LocalEnsembleVotingSystem()
    optimized_system = OptimizedEnsembleVotingSystem()
    
    print(f"   Original System: {len(original_system.models)} models")
    print(f"   Optimized System: {len(optimized_system.models)} models")
    
    # Performance tracking
    results = {
        'original': {'times': [], 'decisions': [], 'models_used': []},
        'optimized': {'times': [], 'decisions': [], 'models_used': [], 'cache_hits': 0}
    }
    
    print("\n🧪 RUNNING PERFORMANCE TESTS")
    print("-" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        symbol = test_case['symbol']
        context = test_case['context']
        
        print(f"\n📈 TEST {i}: {symbol}")
        print("-" * 40)
        
        # Test Original System
        print("🔵 Original System:")
        start_time = time.time()
        try:
            original_decision = await original_system.get_ensemble_prediction(symbol, context)
            original_time = time.time() - start_time
            
            results['original']['times'].append(original_time)
            results['original']['decisions'].append(original_decision)
            results['original']['models_used'].append(original_decision.get('models_used', 0))
            
            print(f"   ✅ Action: {original_decision.get('final_action', 'UNKNOWN')}")
            print(f"   ✅ Confidence: {original_decision.get('ensemble_confidence', 0.0):.2f}")
            print(f"   ✅ Models: {original_decision.get('models_used', 0)}")
            print(f"   ✅ Time: {original_time:.1f}s")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results['original']['times'].append(999)
            results['original']['decisions'].append({'error': str(e)})
            results['original']['models_used'].append(0)
        
        # Test Optimized System
        print("\n🟢 Optimized System:")
        start_time = time.time()
        try:
            optimized_decision = await optimized_system.get_ensemble_prediction(symbol, context)
            optimized_time = time.time() - start_time
            
            results['optimized']['times'].append(optimized_time)
            results['optimized']['decisions'].append(optimized_decision)
            results['optimized']['models_used'].append(optimized_decision.get('models_used', 0))
            
            if optimized_decision.get('cached', False):
                results['optimized']['cache_hits'] += 1
            
            print(f"   ✅ Action: {optimized_decision.get('final_action', 'UNKNOWN')}")
            print(f"   ✅ Confidence: {optimized_decision.get('ensemble_confidence', 0.0):.2f}")
            print(f"   ✅ Models: {optimized_decision.get('models_used', 0)}")
            print(f"   ✅ Time: {optimized_time:.1f}s")
            print(f"   ✅ Cached: {optimized_decision.get('cached', False)}")
            print(f"   ✅ Meta Voting: {optimized_decision.get('meta_voting', False)}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results['optimized']['times'].append(999)
            results['optimized']['decisions'].append({'error': str(e)})
            results['optimized']['models_used'].append(0)
        
        # Performance comparison for this test
        if (len(results['original']['times']) > 0 and 
            len(results['optimized']['times']) > 0 and
            results['original']['times'][-1] < 999 and 
            results['optimized']['times'][-1] < 999):
            
            improvement = ((results['original']['times'][-1] - results['optimized']['times'][-1]) / 
                          results['original']['times'][-1]) * 100
            
            print(f"\n📊 Performance Comparison:")
            print(f"   Original: {results['original']['times'][-1]:.1f}s")
            print(f"   Optimized: {results['optimized']['times'][-1]:.1f}s")
            print(f"   Improvement: {improvement:+.1f}%")
    
    # Test caching performance
    print(f"\n🔄 CACHE PERFORMANCE TEST")
    print("-" * 40)
    
    # Repeat first test to test caching
    test_case = test_cases[0]
    print(f"Repeating {test_case['symbol']} test...")
    
    start_time = time.time()
    cached_decision = await optimized_system.get_ensemble_prediction(test_case['symbol'], test_case['context'])
    cache_time = time.time() - start_time
    
    print(f"   Cache Hit: {cached_decision.get('cached', False)}")
    print(f"   Cache Time: {cache_time:.3f}s")
    
    if cached_decision.get('cached', False):
        original_time = results['optimized']['times'][0]
        cache_improvement = ((original_time - cache_time) / original_time) * 100
        print(f"   Cache Improvement: {cache_improvement:.1f}%")
    
    # Final Performance Report
    print(f"\n📈 FINAL PERFORMANCE REPORT")
    print("=" * 80)
    
    # Calculate averages
    orig_avg_time = sum(t for t in results['original']['times'] if t < 999) / len([t for t in results['original']['times'] if t < 999])
    opt_avg_time = sum(t for t in results['optimized']['times'] if t < 999) / len([t for t in results['optimized']['times'] if t < 999])
    
    orig_avg_models = sum(results['original']['models_used']) / len(results['original']['models_used'])
    opt_avg_models = sum(results['optimized']['models_used']) / len(results['optimized']['models_used'])
    
    overall_improvement = ((orig_avg_time - opt_avg_time) / orig_avg_time) * 100
    
    print(f"\n🔵 ORIGINAL SYSTEM:")
    print(f"   Average Response Time: {orig_avg_time:.1f}s")
    print(f"   Average Models Used: {orig_avg_models:.1f}")
    print(f"   Total Models Available: {len(original_system.models)}")
    print(f"   Caching: Not Available")
    
    print(f"\n🟢 OPTIMIZED SYSTEM:")
    print(f"   Average Response Time: {opt_avg_time:.1f}s")
    print(f"   Average Models Used: {opt_avg_models:.1f}")
    print(f"   Total Models Available: {len(optimized_system.models)}")
    print(f"   Cache Hits: {results['optimized']['cache_hits']}")
    print(f"   Meta Voting: Enabled")
    print(f"   Performance Tracking: Enabled")
    print(f"   Adaptive Timeouts: Enabled")
    
    print(f"\n🏆 PERFORMANCE IMPROVEMENTS:")
    print(f"   Overall Speed Improvement: {overall_improvement:+.1f}%")
    print(f"   Additional Models: +{len(optimized_system.models) - len(original_system.models)}")
    print(f"   Enhanced Features: Caching, Meta-voting, Performance tracking")
    
    # Model performance comparison
    print(f"\n🤖 MODEL PERFORMANCE RANKINGS:")
    rankings = optimized_system.get_model_rankings()
    for i, model in enumerate(rankings[:5], 1):
        print(f"   {i}. {model['model_name'][:50]}...")
        print(f"      Success Rate: {model['success_rate']:.2f}")
        print(f"      Avg Response Time: {model['avg_response_time']:.1f}s")
        print(f"      Performance Score: {model['score']:.3f}")
    
    # Save detailed results
    detailed_results = {
        'timestamp': datetime.now().isoformat(),
        'test_cases': len(test_cases),
        'original_system': {
            'avg_response_time': orig_avg_time,
            'avg_models_used': orig_avg_models,
            'total_models': len(original_system.models),
            'features': ['basic_ensemble_voting']
        },
        'optimized_system': {
            'avg_response_time': opt_avg_time,
            'avg_models_used': opt_avg_models,
            'total_models': len(optimized_system.models),
            'cache_hits': results['optimized']['cache_hits'],
            'features': ['meta_voting', 'caching', 'performance_tracking', 'adaptive_timeouts']
        },
        'improvements': {
            'speed_improvement_percent': overall_improvement,
            'additional_models': len(optimized_system.models) - len(original_system.models),
            'new_features': 4
        },
        'model_rankings': rankings[:10]
    }
    
    with open('performance_comparison_report.json', 'w') as f:
        json.dump(detailed_results, f, indent=2)
    
    print(f"\n💾 Detailed report saved to: performance_comparison_report.json")
    print(f"\n✅ Performance comparison completed!")
    
    # Final verdict
    if overall_improvement > 0:
        print(f"\n🎉 OPTIMIZED SYSTEM IS {overall_improvement:.1f}% FASTER!")
    else:
        print(f"\n⚠️  Optimized system is {abs(overall_improvement):.1f}% slower (but has more features)")
    
    print(f"🚀 The optimized system provides:")
    print(f"   ✅ {len(optimized_system.models)} specialized AI models")
    print(f"   ✅ Advanced meta-voting algorithms")
    print(f"   ✅ Intelligent caching system")
    print(f"   ✅ Performance tracking and optimization")
    print(f"   ✅ Adaptive timeouts")
    print(f"   ✅ Enhanced error handling")

if __name__ == "__main__":
    asyncio.run(performance_comparison())
