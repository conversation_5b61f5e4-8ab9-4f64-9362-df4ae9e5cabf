# LOCAL ENSEMBLE VOTING CONFIGURATION
# Using only local Ollama models for ensemble decisions

ensemble:
  enabled: true
  confidence_threshold: 0.7
  consensus_threshold: 0.6
  voting_strategy: adaptive  # adaptive, weighted, majority, confidence_weighted
  max_parallel_models: 5
  timeout_per_model: 60
  retry_attempts: 2

  # PRIMARY ENSEMBLE MODELS (Top performers)
  models:
  - name: marco-o1:latest
    specialization: reasoning_analysis
    type: ollama
    weight: 0.20
    confidence_threshold: 0.75
    priority: 1

  - name: unrestricted-noryon-deepseek-r1-finance-v2-latest:latest
    specialization: risk_assessment
    type: ollama
    weight: 0.18
    confidence_threshold: 0.70
    priority: 2

  - name: unrestricted-noryon-phi-4-9b-finance-latest:latest
    specialization: technical_analysis
    type: ollama
    weight: 0.17
    confidence_threshold: 0.70
    priority: 3

  - name: unrestricted-noryon-qwen3-finance-v2-latest:latest
    specialization: market_analysis
    type: ollama
    weight: 0.16
    confidence_threshold: 0.65
    priority: 4

  - name: unrestricted-noryon-gemma-3-12b-finance-latest:latest
    specialization: fundamental_analysis
    type: ollama
    weight: 0.15
    confidence_threshold: 0.65
    priority: 5

  - name: unrestricted-noryon-cogito-finance-v2-latest:latest
    specialization: pattern_recognition
    type: ollama
    weight: 0.14
    confidence_threshold: 0.60
    priority: 6
