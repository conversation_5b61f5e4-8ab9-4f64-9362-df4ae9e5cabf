# OPTIMIZED LOCAL ENSEMBLE VOTING CONFIGURATION
# Enhanced with 10+ specialized AI models and performance optimizations

ensemble:
  enabled: true
  confidence_threshold: 0.7
  consensus_threshold: 0.6
  voting_strategy: adaptive  # adaptive, weighted, majority, confidence_weighted

  # PERFORMANCE OPTIMIZATIONS
  max_parallel_models: 8  # Increased for better performance
  timeout_per_model: 45   # Reduced for faster responses
  retry_attempts: 2
  cache_enabled: true
  cache_ttl: 300  # 5 minutes
  adaptive_timeouts: true
  performance_tracking: true

  # ENHANCED ENSEMBLE MODELS (10+ specialized models)
  models:
  # TIER 1: Core Reasoning & Analysis (High Priority)
  - name: marco-o1:latest
    specialization: reasoning_analysis
    type: ollama
    weight: 0.15
    confidence_threshold: 0.75
    priority: 1
    timeout: 45
    enabled: true

  - name: unrestricted-noryon-deepseek-r1-finance-v2-latest:latest
    specialization: risk_assessment
    type: ollama
    weight: 0.14
    confidence_threshold: 0.70
    priority: 2
    timeout: 50
    enabled: true

  - name: unrestricted-noryon-phi-4-9b-finance-latest:latest
    specialization: technical_analysis
    type: ollama
    weight: 0.13
    confidence_threshold: 0.70
    priority: 3
    timeout: 35
    enabled: true

  - name: unrestricted-noryon-qwen3-finance-v2-latest:latest
    specialization: market_analysis
    type: ollama
    weight: 0.12
    confidence_threshold: 0.65
    priority: 4
    timeout: 40
    enabled: true

  - name: unrestricted-noryon-gemma-3-12b-finance-latest:latest
    specialization: fundamental_analysis
    type: ollama
    weight: 0.11
    confidence_threshold: 0.65
    priority: 5
    timeout: 45
    enabled: true

  - name: unrestricted-noryon-cogito-finance-v2-latest:latest
    specialization: pattern_recognition
    type: ollama
    weight: 0.10
    confidence_threshold: 0.60
    priority: 6
    timeout: 40
    enabled: true

  # TIER 2: Advanced Specialists (Medium Priority)
  - name: unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest
    specialization: advanced_reasoning
    type: ollama
    weight: 0.09
    confidence_threshold: 0.70
    priority: 7
    timeout: 55
    enabled: true

  - name: unrestricted-noryon-exaone-deep-finance-v2-latest:latest
    specialization: deep_analysis
    type: ollama
    weight: 0.08
    confidence_threshold: 0.65
    priority: 8
    timeout: 40
    enabled: true

  - name: unrestricted-noryon-falcon3-finance-v1-latest:latest
    specialization: momentum_analysis
    type: ollama
    weight: 0.07
    confidence_threshold: 0.60
    priority: 9
    timeout: 35
    enabled: true

  - name: unrestricted-noryon-deepscaler-finance-v2-latest:latest
    specialization: scalability_analysis
    type: ollama
    weight: 0.06
    confidence_threshold: 0.60
    priority: 10
    timeout: 30
    enabled: true

  # TIER 3: Additional Specialists (Lower Priority - Can be enabled as needed)
  - name: unrestricted-noryon-dolphin3-finance-v2-latest:latest
    specialization: sentiment_analysis
    type: ollama
    weight: 0.05
    confidence_threshold: 0.60
    priority: 11
    timeout: 35
    enabled: false  # Disabled by default - enable if needed

  - name: enhanced-unrestricted-phi4-14b-latest:latest
    specialization: enhanced_technical
    type: ollama
    weight: 0.04
    confidence_threshold: 0.65
    priority: 12
    timeout: 50
    enabled: false  # Disabled by default

  - name: smart-unrestricted-qwen3-14b-latest:latest
    specialization: smart_analysis
    type: ollama
    weight: 0.04
    confidence_threshold: 0.65
    priority: 13
    timeout: 45
    enabled: false  # Disabled by default

  - name: expert-unrestricted-qwen3-14b-latest:latest
    specialization: expert_analysis
    type: ollama
    weight: 0.03
    confidence_threshold: 0.70
    priority: 14
    timeout: 45
    enabled: false  # Disabled by default

  - name: enhanced-wizard-math-13b:latest
    specialization: mathematical_analysis
    type: ollama
    weight: 0.03
    confidence_threshold: 0.65
    priority: 15
    timeout: 40
    enabled: false  # Disabled by default

# VOTING STRATEGIES CONFIGURATION
voting_strategies:
  weighted:
    enabled: true
    description: "Standard weighted voting based on model weights"

  confidence_weighted:
    enabled: true
    description: "Voting weighted by model confidence scores"

  performance_weighted:
    enabled: true
    description: "Voting weighted by historical model performance"

  meta_voting:
    enabled: true
    description: "Combines multiple voting strategies"

# PERFORMANCE MONITORING
performance:
  tracking_enabled: true
  metrics_retention_days: 30
  auto_weight_optimization: true
  optimization_frequency_hours: 24

# CACHING CONFIGURATION
cache:
  enabled: true
  ttl_seconds: 300
  max_entries: 100
  cleanup_frequency_minutes: 60

# HEALTH CHECK CONFIGURATION
health_check:
  enabled: true
  check_frequency_minutes: 30
  test_timeout_seconds: 15
  alert_on_failures: true
