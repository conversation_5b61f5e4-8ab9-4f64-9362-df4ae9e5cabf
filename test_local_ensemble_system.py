#!/usr/bin/env python3
"""
Comprehensive Test Suite for Local Ensemble Voting System
Tests all functionality with real local models - NO FAKE TESTS
"""

import asyncio
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Any

# Import our local ensemble system
from local_ensemble_voting_system import LocalEnsembleVotingSystem
from optimized_model_caller import EnhancedLocalModelCaller

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LocalEnsembleSystemTester:
    """Comprehensive tester for local ensemble system"""
    
    def __init__(self):
        self.ensemble = None
        self.model_caller = EnhancedLocalModelCaller()
        self.test_results = {}
        self.start_time = None
        
    async def run_comprehensive_tests(self):
        """Run all tests and provide detailed results"""
        self.start_time = time.time()
        
        print("🚀 STARTING COMPREHENSIVE LOCAL ENSEMBLE TESTS")
        print("=" * 80)
        
        # Test 1: System Initialization
        await self.test_system_initialization()
        
        # Test 2: Model Availability
        await self.test_model_availability()
        
        # Test 3: Individual Model Calls
        await self.test_individual_model_calls()
        
        # Test 4: Ensemble Voting
        await self.test_ensemble_voting()
        
        # Test 5: Performance Benchmarking
        await self.test_performance_benchmarking()
        
        # Test 6: Error Handling
        await self.test_error_handling()
        
        # Generate comprehensive report
        await self.generate_test_report()
        
    async def test_system_initialization(self):
        """Test system initialization"""
        print("\n📋 TEST 1: System Initialization")
        print("-" * 50)
        
        try:
            self.ensemble = LocalEnsembleVotingSystem()
            
            # Verify initialization
            assert self.ensemble is not None, "Ensemble system not initialized"
            assert len(self.ensemble.models) > 0, "No models loaded"
            
            print(f"✅ System initialized successfully")
            print(f"   Models loaded: {len(self.ensemble.models)}")
            print(f"   Configuration: {self.ensemble.config_path}")
            
            # List available models
            print("\n📊 Available Models:")
            for model_name, config in self.ensemble.models.items():
                print(f"   • {model_name}")
                print(f"     Specialization: {config['specialization']}")
                print(f"     Weight: {config['weight']}")
                print(f"     Confidence Threshold: {config['confidence_threshold']}")
            
            self.test_results['initialization'] = {
                'status': 'PASSED',
                'models_count': len(self.ensemble.models),
                'details': 'System initialized successfully'
            }
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            self.test_results['initialization'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            
    async def test_model_availability(self):
        """Test if models are actually available in Ollama"""
        print("\n📋 TEST 2: Model Availability Check")
        print("-" * 50)
        
        try:
            available_models = await self.model_caller.get_available_models()
            
            print(f"📊 Ollama Models Available: {len(available_models)}")
            
            # Check which ensemble models are actually available
            available_ensemble_models = []
            missing_models = []
            
            for model_name in self.ensemble.models.keys():
                if model_name in available_models:
                    available_ensemble_models.append(model_name)
                    print(f"   ✅ {model_name}")
                else:
                    missing_models.append(model_name)
                    print(f"   ❌ {model_name} (NOT AVAILABLE)")
            
            self.test_results['model_availability'] = {
                'status': 'PASSED' if available_ensemble_models else 'FAILED',
                'available_models': len(available_ensemble_models),
                'missing_models': len(missing_models),
                'available_list': available_ensemble_models,
                'missing_list': missing_models
            }
            
            if not available_ensemble_models:
                print("⚠️  WARNING: No ensemble models are available in Ollama!")
                
        except Exception as e:
            print(f"❌ Model availability check failed: {e}")
            self.test_results['model_availability'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_individual_model_calls(self):
        """Test individual model calls"""
        print("\n📋 TEST 3: Individual Model Calls")
        print("-" * 50)
        
        test_prompt = """
Analyze AAPL for trading decision. Provide:

1. ACTION: BUY/SELL/HOLD
2. CONFIDENCE: 0.0-1.0
3. REASONING: Brief explanation

Respond in format:
ACTION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [explanation]
"""
        
        successful_calls = 0
        failed_calls = 0
        call_results = {}
        
        # Test first 3 models to avoid long wait times
        test_models = list(self.ensemble.models.keys())[:3]
        
        for model_name in test_models:
            print(f"\n🤖 Testing {model_name}...")
            
            try:
                start_time = time.time()
                response = self.model_caller.call_model_safe(model_name, test_prompt, timeout=45)
                call_time = time.time() - start_time
                
                if response.success:
                    print(f"   ✅ Success ({call_time:.1f}s)")
                    print(f"   Response: {response.response[:100]}...")
                    successful_calls += 1
                    call_results[model_name] = {
                        'status': 'SUCCESS',
                        'response_time': call_time,
                        'response_length': len(response.response)
                    }
                else:
                    print(f"   ❌ Failed: {response.error_message}")
                    failed_calls += 1
                    call_results[model_name] = {
                        'status': 'FAILED',
                        'error': response.error_message
                    }
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
                failed_calls += 1
                call_results[model_name] = {
                    'status': 'EXCEPTION',
                    'error': str(e)
                }
        
        self.test_results['individual_calls'] = {
            'status': 'PASSED' if successful_calls > 0 else 'FAILED',
            'successful_calls': successful_calls,
            'failed_calls': failed_calls,
            'call_results': call_results
        }
        
        print(f"\n📊 Individual Call Results:")
        print(f"   Successful: {successful_calls}")
        print(f"   Failed: {failed_calls}")
    
    async def test_ensemble_voting(self):
        """Test ensemble voting functionality"""
        print("\n📋 TEST 4: Ensemble Voting")
        print("-" * 50)
        
        test_symbols = ["AAPL", "TSLA"]
        voting_results = {}
        
        for symbol in test_symbols:
            print(f"\n📈 Testing ensemble voting for {symbol}...")
            
            try:
                market_context = {
                    "market_trend": "bullish",
                    "volatility": "medium",
                    "volume": "high",
                    "news_sentiment": "positive"
                }
                
                start_time = time.time()
                decision = await self.ensemble.get_ensemble_prediction(symbol, market_context)
                voting_time = time.time() - start_time
                
                print(f"   ✅ Ensemble decision completed ({voting_time:.1f}s)")
                print(f"   Final Action: {decision.get('final_action', 'UNKNOWN')}")
                print(f"   Confidence: {decision.get('ensemble_confidence', 0.0):.2f}")
                print(f"   Models Used: {decision.get('models_used', 0)}")
                print(f"   Consensus: {decision.get('consensus_level', 0.0):.2f}")
                
                voting_results[symbol] = {
                    'status': 'SUCCESS',
                    'decision': decision,
                    'voting_time': voting_time
                }
                
            except Exception as e:
                print(f"   ❌ Ensemble voting failed: {e}")
                voting_results[symbol] = {
                    'status': 'FAILED',
                    'error': str(e)
                }
        
        self.test_results['ensemble_voting'] = {
            'status': 'PASSED' if any(r['status'] == 'SUCCESS' for r in voting_results.values()) else 'FAILED',
            'results': voting_results
        }
    
    async def test_performance_benchmarking(self):
        """Test system performance"""
        print("\n📋 TEST 5: Performance Benchmarking")
        print("-" * 50)
        
        # Quick performance test
        start_time = time.time()
        
        try:
            decision = await self.ensemble.get_ensemble_prediction("BTC", {"trend": "bullish"})
            total_time = time.time() - start_time
            
            print(f"📊 Performance Metrics:")
            print(f"   Total Response Time: {total_time:.2f}s")
            print(f"   Models Responded: {decision.get('models_used', 0)}")
            print(f"   Average Time per Model: {total_time / max(1, decision.get('models_used', 1)):.2f}s")
            
            # Performance thresholds
            performance_grade = "EXCELLENT" if total_time < 30 else "GOOD" if total_time < 60 else "SLOW"
            
            self.test_results['performance'] = {
                'status': 'PASSED',
                'total_time': total_time,
                'models_used': decision.get('models_used', 0),
                'performance_grade': performance_grade
            }
            
        except Exception as e:
            print(f"❌ Performance test failed: {e}")
            self.test_results['performance'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def test_error_handling(self):
        """Test error handling capabilities"""
        print("\n📋 TEST 6: Error Handling")
        print("-" * 50)
        
        try:
            # Test with invalid model
            response = self.model_caller.call_model_safe("nonexistent-model", "test prompt")
            
            if not response.success:
                print("   ✅ Error handling works for invalid models")
                error_handling_status = 'PASSED'
            else:
                print("   ❌ Error handling failed - invalid model returned success")
                error_handling_status = 'FAILED'
                
            self.test_results['error_handling'] = {
                'status': error_handling_status,
                'details': 'Tested invalid model handling'
            }
            
        except Exception as e:
            print(f"   ❌ Error handling test failed: {e}")
            self.test_results['error_handling'] = {
                'status': 'FAILED',
                'error': str(e)
            }
    
    async def generate_test_report(self):
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        passed_tests = sum(1 for result in self.test_results.values() if result.get('status') == 'PASSED')
        total_tests = len(self.test_results)
        
        print(f"🎯 Overall Results: {passed_tests}/{total_tests} tests passed")
        print(f"⏱️  Total Test Time: {total_time:.2f} seconds")
        print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n📋 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get('status') == 'PASSED' else "❌"
            print(f"   {status_icon} {test_name.upper()}: {result.get('status', 'UNKNOWN')}")
            
            if result.get('status') == 'FAILED' and 'error' in result:
                print(f"      Error: {result['error']}")
        
        # Save detailed report
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'total_time': total_time,
            'passed_tests': passed_tests,
            'total_tests': total_tests,
            'success_rate': passed_tests / total_tests if total_tests > 0 else 0,
            'detailed_results': self.test_results
        }
        
        with open('local_ensemble_test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n💾 Detailed report saved to: local_ensemble_test_report.json")
        
        # Final verdict
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! Local Ensemble System is fully functional.")
        elif passed_tests > total_tests // 2:
            print(f"\n⚠️  PARTIAL SUCCESS: {passed_tests}/{total_tests} tests passed. System is partially functional.")
        else:
            print(f"\n❌ SYSTEM FAILURE: Only {passed_tests}/{total_tests} tests passed. Major issues detected.")
        
        # Close resources
        await self.model_caller.close()

async def main():
    """Main test execution"""
    tester = LocalEnsembleSystemTester()
    await tester.run_comprehensive_tests()

if __name__ == "__main__":
    asyncio.run(main())
