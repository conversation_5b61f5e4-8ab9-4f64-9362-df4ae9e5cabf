#!/usr/bin/env python3
"""
ULTIMATE TRADING SYSTEM LAUNCHER
Ready for everything - blazing fast, rock-solid reliable
"""

import asyncio
import time
import json
from datetime import datetime
from typing import Dict, Any, List
from ultimate_ensemble_system import UltimateEnsembleSystem, TradingDecision

class UltimateTradingLauncher:
    """Ultimate launcher for all trading scenarios"""
    
    def __init__(self):
        print("🚀 ULTIMATE TRADING SYSTEM LAUNCHER")
        print("=" * 60)
        print("🔥 Ready for EVERYTHING - Maximum Performance Mode")
        print()
        
        self.system = None
        self.session_stats = {
            'decisions_made': 0,
            'total_response_time': 0.0,
            'cache_hits': 0,
            'start_time': datetime.now()
        }
    
    async def initialize_system(self):
        """Initialize the ultimate system"""
        print("🔧 Initializing Ultimate Ensemble System...")
        
        start_time = time.time()
        self.system = UltimateEnsembleSystem()
        init_time = time.time() - start_time
        
        print(f"✅ System initialized in {init_time:.1f}s")
        
        # Quick health check
        print("🏥 Running health check...")
        health = await self.system.health_check()
        print(f"✅ System status: {health['overall_status']}")
        print(f"✅ Healthy models: {health['healthy_models']}/{health['total_tested']}")
        print()
    
    async def get_trading_decision(self, symbol: str, urgency: str = "normal", 
                                 market_context: Dict[str, Any] = None) -> TradingDecision:
        """Get trading decision with full performance tracking"""
        
        if not self.system:
            await self.initialize_system()
        
        print(f"🎯 Getting {urgency.upper()} trading decision for {symbol}...")
        
        start_time = time.time()
        decision = await self.system.get_ultimate_trading_decision(symbol, market_context, urgency)
        total_time = time.time() - start_time
        
        # Update session stats
        self.session_stats['decisions_made'] += 1
        self.session_stats['total_response_time'] += total_time
        if decision.cache_hit:
            self.session_stats['cache_hits'] += 1
        
        # Display results
        self._display_decision(decision)
        
        return decision
    
    def _display_decision(self, decision: TradingDecision):
        """Display trading decision with full details"""
        
        print(f"\n📊 TRADING DECISION RESULTS:")
        print(f"   🎯 Symbol: {decision.symbol}")
        print(f"   📈 Action: {decision.final_action}")
        print(f"   🎲 Confidence: {decision.ensemble_confidence:.2f}")
        print(f"   🤝 Consensus: {decision.consensus_level:.2f}")
        print(f"   🤖 Models Used: {decision.models_used}")
        print(f"   ⚡ Response Time: {decision.response_time:.1f}s")
        print(f"   🛡️ Risk Level: {decision.risk_assessment['risk_level']}")
        print(f"   💾 Cached: {'Yes' if decision.cache_hit else 'No'}")
        print(f"   🧠 Meta Voting: {'Yes' if decision.meta_voting else 'No'}")
        
        # Risk assessment details
        risk = decision.risk_assessment
        print(f"\n🛡️ RISK ASSESSMENT:")
        print(f"   Overall Risk: {risk['overall_risk']:.2f}")
        print(f"   Risk Level: {risk['risk_level']}")
        print(f"   Recommendation: {risk['recommendation']}")
        
        # Voting strategies summary
        if decision.voting_strategies:
            print(f"\n🗳️ VOTING STRATEGIES:")
            for strategy, result in decision.voting_strategies.items():
                print(f"   {strategy}: {result['action']} ({result['confidence']:.2f})")
        
        print()
    
    async def emergency_decision(self, symbol: str, market_context: Dict[str, Any] = None) -> TradingDecision:
        """Get emergency trading decision (< 20s)"""
        print(f"🚨 EMERGENCY DECISION REQUESTED for {symbol}")
        return await self.get_trading_decision(symbol, "emergency", market_context)
    
    async def fast_decision(self, symbol: str, market_context: Dict[str, Any] = None) -> TradingDecision:
        """Get fast trading decision (< 30s)"""
        print(f"⚡ FAST DECISION REQUESTED for {symbol}")
        return await self.get_trading_decision(symbol, "fast", market_context)
    
    async def comprehensive_analysis(self, symbol: str, market_context: Dict[str, Any] = None) -> TradingDecision:
        """Get comprehensive trading analysis (all models)"""
        print(f"🔬 COMPREHENSIVE ANALYSIS REQUESTED for {symbol}")
        return await self.get_trading_decision(symbol, "comprehensive", market_context)
    
    async def batch_analysis(self, symbols: List[str], urgency: str = "normal") -> List[TradingDecision]:
        """Analyze multiple symbols efficiently"""
        print(f"📊 BATCH ANALYSIS: {len(symbols)} symbols ({urgency} urgency)")
        print("-" * 60)
        
        decisions = []
        for i, symbol in enumerate(symbols, 1):
            print(f"\n[{i}/{len(symbols)}] Analyzing {symbol}...")
            decision = await self.get_trading_decision(symbol, urgency)
            decisions.append(decision)
        
        # Batch summary
        print(f"\n📈 BATCH ANALYSIS COMPLETE:")
        print(f"   Symbols Analyzed: {len(symbols)}")
        print(f"   Total Time: {sum(d.response_time for d in decisions):.1f}s")
        print(f"   Avg Time per Symbol: {sum(d.response_time for d in decisions) / len(decisions):.1f}s")
        
        # Action summary
        actions = {}
        for decision in decisions:
            actions[decision.final_action] = actions.get(decision.final_action, 0) + 1
        
        print(f"   Action Distribution:")
        for action, count in actions.items():
            print(f"     {action}: {count} ({count/len(decisions)*100:.1f}%)")
        
        return decisions
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session performance statistics"""
        runtime = (datetime.now() - self.session_stats['start_time']).total_seconds()
        avg_response_time = (self.session_stats['total_response_time'] / 
                           max(1, self.session_stats['decisions_made']))
        cache_hit_rate = (self.session_stats['cache_hits'] / 
                         max(1, self.session_stats['decisions_made']))
        
        return {
            'session_runtime': runtime,
            'decisions_made': self.session_stats['decisions_made'],
            'avg_response_time': avg_response_time,
            'cache_hit_rate': cache_hit_rate,
            'decisions_per_minute': self.session_stats['decisions_made'] / max(1, runtime / 60)
        }
    
    async def performance_benchmark(self):
        """Run comprehensive performance benchmark"""
        print("🏁 PERFORMANCE BENCHMARK")
        print("=" * 60)
        
        # Test different urgency levels
        test_cases = [
            ("AAPL", "emergency", {"trend": "bullish", "volatility": "high"}),
            ("TSLA", "fast", {"trend": "volatile", "volatility": "medium"}),
            ("BTC", "normal", {"trend": "bearish", "volatility": "extreme"}),
            ("NVDA", "comprehensive", {"trend": "bullish", "volatility": "low"})
        ]
        
        benchmark_results = []
        
        for symbol, urgency, context in test_cases:
            print(f"\n🧪 Benchmarking {symbol} ({urgency})...")
            
            # Run multiple times for average
            times = []
            for run in range(3):
                start_time = time.time()
                decision = await self.get_trading_decision(symbol, urgency, context)
                times.append(time.time() - start_time)
            
            avg_time = sum(times) / len(times)
            benchmark_results.append({
                'symbol': symbol,
                'urgency': urgency,
                'avg_time': avg_time,
                'min_time': min(times),
                'max_time': max(times)
            })
        
        # Benchmark summary
        print(f"\n🏆 BENCHMARK RESULTS:")
        print("-" * 60)
        for result in benchmark_results:
            print(f"   {result['symbol']} ({result['urgency']}):")
            print(f"     Avg: {result['avg_time']:.1f}s")
            print(f"     Range: {result['min_time']:.1f}s - {result['max_time']:.1f}s")
        
        return benchmark_results
    
    async def demo_all_features(self):
        """Demonstrate all system features"""
        print("🎪 ULTIMATE SYSTEM FEATURE DEMONSTRATION")
        print("=" * 60)
        
        # 1. Emergency decision
        print("\n1️⃣ EMERGENCY DECISION (Lightning Fast)")
        await self.emergency_decision("AAPL", {"trend": "bearish", "volatility": "high"})
        
        # 2. Fast decision
        print("\n2️⃣ FAST DECISION (Quick Analysis)")
        await self.fast_decision("TSLA", {"trend": "bullish", "volatility": "medium"})
        
        # 3. Normal decision
        print("\n3️⃣ NORMAL DECISION (Balanced)")
        await self.get_trading_decision("BTC", "normal", {"trend": "volatile", "volatility": "extreme"})
        
        # 4. Comprehensive analysis
        print("\n4️⃣ COMPREHENSIVE ANALYSIS (All Models)")
        await self.comprehensive_analysis("NVDA", {"trend": "bullish", "volatility": "low"})
        
        # 5. Batch analysis
        print("\n5️⃣ BATCH ANALYSIS (Multiple Symbols)")
        await self.batch_analysis(["MSFT", "GOOGL", "AMZN"], "fast")
        
        # 6. Cache demonstration
        print("\n6️⃣ CACHE DEMONSTRATION (Repeat Query)")
        await self.get_trading_decision("AAPL", "emergency", {"trend": "bearish", "volatility": "high"})
        
        # Session statistics
        print("\n📊 SESSION STATISTICS:")
        stats = self.get_session_stats()
        print(f"   Total Decisions: {stats['decisions_made']}")
        print(f"   Avg Response Time: {stats['avg_response_time']:.1f}s")
        print(f"   Cache Hit Rate: {stats['cache_hit_rate']:.1%}")
        print(f"   Decisions/Minute: {stats['decisions_per_minute']:.1f}")
        
        print(f"\n🎉 ALL FEATURES DEMONSTRATED SUCCESSFULLY!")

# Quick usage functions
async def quick_decision(symbol: str, urgency: str = "normal") -> TradingDecision:
    """Quick single decision"""
    launcher = UltimateTradingLauncher()
    return await launcher.get_trading_decision(symbol, urgency)

async def emergency_trade(symbol: str) -> TradingDecision:
    """Emergency trading decision"""
    launcher = UltimateTradingLauncher()
    return await launcher.emergency_decision(symbol)

async def analyze_portfolio(symbols: List[str]) -> List[TradingDecision]:
    """Analyze entire portfolio"""
    launcher = UltimateTradingLauncher()
    return await launcher.batch_analysis(symbols, "fast")

# Main execution
async def main():
    """Main demonstration"""
    launcher = UltimateTradingLauncher()
    await launcher.demo_all_features()

if __name__ == "__main__":
    asyncio.run(main())
